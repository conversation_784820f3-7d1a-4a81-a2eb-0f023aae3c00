const { chromium } = require('playwright');

async function testIoTVisionWebsite() {
  console.log('🚀 Spouštím testování IoTVision webu...');

  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const context = await browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });

    const page = await context.newPage();

    // Zachytávání chyb v konzoli
    const consoleErrors = [];
    const networkErrors = [];

    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    page.on('response', response => {
      if (!response.ok()) {
        networkErrors.push(`${response.status()} ${response.url()}`);
      }
    });

    console.log('📡 Připojuji se na http://localhost...');

    // Načtení hlavn<PERSON> stránky
    await page.goto('http://localhost', {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    console.log('✅ Stránka úspěšně načtena');

    // Test základních elementů
    console.log('🔍 Testuji základní elementy...');

    // Test hlavičky
    const header = await page.locator('header').first();
    await header.waitFor({ timeout: 10000 });
    console.log('✅ Header nalezen');

    // Test loga
    const logo = await page.locator('.logo-text').first();
    await logo.waitFor({ timeout: 5000 });
    const logoText = await logo.textContent();
    console.log(`✅ Logo: ${logoText}`);

    // Test navigace
    const navLinks = await page.locator('.nav__link').all();
    console.log(`✅ Navigační odkazy: ${navLinks.length}`);

    // Test přepínače jazyků v patičce
    console.log('🌍 Testuji přepínač jazyků v patičce...');
    const languageButtons = await page.locator('.language-btn').all();
    if (languageButtons.length > 0) {
      console.log('✅ Přepínače jazyků nalezeny v patičce');

      // Test přepnutí jazyka
      const csButton = await page.locator('.language-btn').first();
      await csButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ Přepínání jazyků funguje správně');
    } else {
      console.log('⚠️  Přepínače jazyků nenalezeny');
    }

    // Test hero sekce
    console.log('🎯 Testuji hero sekci...');
    const heroTitle = await page.locator('.hero__title, h1').first();
    await heroTitle.waitFor({ timeout: 5000 });
    const titleText = await heroTitle.textContent();
    console.log(`✅ Hero title: ${titleText.substring(0, 50)}...`);

    // Test tlačítek
    console.log('🔘 Testuji tlačítka...');
    const buttons = await page.locator('button, .btn').all();
    console.log(`✅ Nalezeno tlačítek: ${buttons.length}`);

    // Test responsivity - mobile
    console.log('📱 Testuji mobilní zobrazení...');
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);

    const mobileMenu = await page.locator('.nav__toggle').first();
    await mobileMenu.waitFor({ timeout: 5000 });
    console.log('✅ Mobilní menu nalezeno');

    // Test kliknutí na mobilní menu
    await mobileMenu.click();
    await page.waitForTimeout(500);
    console.log('✅ Mobilní menu kliknutelné');

    // Návrat na desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);

    // Test navigace na další stránky
    console.log('🔗 Testuji navigaci...');
    const pages = ['/solutions', '/technologies', '/about', '/contact'];

    for (const pagePath of pages) {
      try {
        await page.goto(`http://localhost${pagePath}`, {
          waitUntil: 'networkidle',
          timeout: 15000
        });
        console.log(`✅ Stránka ${pagePath} načtena úspěšně`);

        // Krátká pauza mezi stránkami
        await page.waitForTimeout(1000);
      } catch (error) {
        console.log(`❌ Chyba při načítání ${pagePath}: ${error.message}`);
      }
    }

    // Návrat na hlavní stránku
    await page.goto('http://localhost', { waitUntil: 'networkidle' });

    // Test animací a efektů
    console.log('✨ Testuji animace...');
    const particleBackground = await page.locator('.particle-background, canvas').first();
    if (await particleBackground.isVisible()) {
      console.log('✅ Částicové pozadí nalezeno');
    } else {
      console.log('⚠️  Částicové pozadí nenalezeno');
    }

    // Výsledky testování
    console.log('\n📊 VÝSLEDKY TESTOVÁNÍ:');
    console.log('========================');

    if (consoleErrors.length === 0) {
      console.log('✅ Žádné chyby v konzoli');
    } else {
      console.log(`❌ Chyby v konzoli (${consoleErrors.length}):`);
      consoleErrors.forEach(error => console.log(`   - ${error}`));
    }

    if (networkErrors.length === 0) {
      console.log('✅ Žádné síťové chyby');
    } else {
      console.log(`❌ Síťové chyby (${networkErrors.length}):`);
      networkErrors.forEach(error => console.log(`   - ${error}`));
    }

    // Test výkonu
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        loadTime: Math.round(navigation.loadEventEnd - navigation.fetchStart),
        domContentLoaded: Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart),
        firstPaint: Math.round(performance.getEntriesByType('paint')[0]?.startTime || 0)
      };
    });

    console.log('\n⚡ VÝKONNOSTNÍ METRIKY:');
    console.log('=======================');
    console.log(`📈 Celkový čas načtení: ${performanceMetrics.loadTime}ms`);
    console.log(`📈 DOM Content Loaded: ${performanceMetrics.domContentLoaded}ms`);
    console.log(`📈 First Paint: ${performanceMetrics.firstPaint}ms`);

    if (performanceMetrics.loadTime < 3000) {
      console.log('✅ Výkon: Výborný (< 3s)');
    } else if (performanceMetrics.loadTime < 5000) {
      console.log('⚠️  Výkon: Dobrý (< 5s)');
    } else {
      console.log('❌ Výkon: Pomalý (> 5s)');
    }

    console.log('\n🎉 Testování dokončeno úspěšně!');

    return {
      success: true,
      consoleErrors: consoleErrors.length,
      networkErrors: networkErrors.length,
      performance: performanceMetrics
    };

  } catch (error) {
    console.error('❌ Chyba během testování:', error.message);
    return {
      success: false,
      error: error.message
    };
  } finally {
    await browser.close();
  }
}

// Spuštění testů
testIoTVisionWebsite()
  .then(result => {
    if (result.success) {
      console.log('\n✅ VŠECHNY TESTY PROŠLY ÚSPĚŠNĚ!');
      process.exit(0);
    } else {
      console.log('\n❌ TESTY SELHALY!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
  });
