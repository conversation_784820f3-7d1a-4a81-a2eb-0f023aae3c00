# IoTVision

🚀 **Moderní IoT platforma s Vue.js 3 frontendem, kompletní internacionalizací a kontejnerizovaným nasazením**

Moderní, plně responzivní webová aplikace postavená na Vue.js 3 s pokroč<PERSON><PERSON><PERSON> animacemi, českou lokalizací a profesionálním designem.

## ✨ Hlavní funkce

- 🌍 **Česká lokalizace** - Kompletní překlad do češtiny s možností přepínání jazyků
- 🎨 **Futuristický design** s temnou elegancí a neonovými akcenty
- ⚡ **Vue.js 3** s Composition API a TypeScript
- 🎭 **Canvas animace** a interaktivní částicové efekty
- 📱 **Plně responzivní** design pro všechna zařízení
- 📧 **Funkční kontaktní formulář** s validací a zpětnou vazbou
- 🐳 **<PERSON>er kontejnerizace** pro snadné nasazení
- 🔧 **Produkční optimalizace** s <PERSON><PERSON><PERSON>, gzip kompresí a bezpečnostními hlavičkami

## 🎨 Barevná paleta

### Základní barvy
- **Primární pozadí**: `#0A0A0F` (velmi tmavá)
- **Sekundární pozadí**: `#1A1A2E` (tmavě modrá)
- **Terciární pozadí**: `#2C2C3E` (antracitová)

### Neonové akcenty
- **Aqua**: `#00FFFF` (hlavní akcentová)
- **Zelená**: `#00FF7F` (úspěch, pozitivní akce)
- **Fuchsie**: `#FF00FF` (zvýraznění, call-to-action)

## 🛠️ Technologie

- **Frontend**: Vue.js 3, Vite, TypeScript
- **Lokalizace**: Vue I18n s českou lokalizací
- **Styling**: SCSS s CSS Grid a Flexbox
- **Animace**: CSS animace, HTML5 Canvas
- **Správa stavu**: Pinia
- **Routing**: Vue Router
- **Kontejnerizace**: Docker, Nginx s optimalizacemi
- **Build nástroj**: Vite s produkčními optimalizacemi

## 🚀 Rychlý start

### Vývojové prostředí

```bash
# Klonování repozitáře
git clone <repository-url>
cd iotvision

# Instalace závislostí
cd iotvision-frontend
npm install

# Spuštění dev serveru
npm run dev
```

Aplikace bude dostupná na `http://localhost:5173`

## 🌍 Lokalizace

Aplikace podporuje přepínání mezi jazyky:
- **Čeština (cs)**: Hlavní jazyk aplikace
- **Angličtina (en)**: Sekundární jazyk

### Přidání nových překladů
1. Přidejte nové klíče do `src/i18n/locales/en.json`
2. Přidejte odpovídající překlady do `src/i18n/locales/cs.json`
3. Použijte v komponentách: `{{ $t('your.key') }}`

## 🐳 Docker nasazení

```bash
# Spuštění pomocí Docker Compose
docker-compose up --build -d

# Nebo manuální build a spuštění
docker build -t iotvision-frontend:latest ./iotvision-frontend
docker run -d -p 80:80 iotvision-frontend:latest
```

Aplikace bude dostupná na `http://localhost`

## 🚀 Produkční nasazení

```bash
# Nasazení do produkce
./scripts/deploy.sh production

# Nebo pomocí Docker Compose
docker-compose -f docker-compose.prod.yml up -d
```

## 📁 Struktura projektu

```
iotvision/
├── iotvision-frontend/          # Vue.js aplikace
│   ├── src/
│   │   ├── components/          # Vue komponenty
│   │   │   ├── layout/          # Layout komponenty
│   │   │   ├── sections/        # Sekce stránky
│   │   │   └── effects/         # Animační efekty
│   │   ├── views/               # Stránky
│   │   ├── styles/              # SCSS styly
│   │   └── router/              # Vue Router konfigurace
│   ├── Dockerfile               # Docker konfigurace
│   └── nginx.conf               # Nginx konfigurace
├── scripts/                     # Deployment skripty
├── docker-compose.yml           # Docker Compose pro dev
├── docker-compose.prod.yml      # Docker Compose pro produkci
└── README.md                    # Dokumentace
```

## 🎭 Animace a efekty

### Canvas animace
- Plovoucí částice v pozadí s interakcí myši
- Síťové spojení mezi uzly
- Datové toky a vizualizace
- Responzivní animace

### CSS animace
- Fade-in efekty při scrollování
- Hover animace s neonovým svícením
- Pulsující efekty
- Smooth transitions

## 🌟 Funkce

### Stránky
- **Home**: Hero sekce s animacemi, funkce, technologie
- **Solutions**: IoT řešení pro různá odvětví
- **Technologies**: Detailní přehled technologií
- **About**: Informace o společnosti a týmu
- **Contact**: Kontaktní formulář a informace

### Komponenty
- Responzivní navigace s mobilním menu
- Částicové pozadí s Canvas animacemi
- Interaktivní karty s hover efekty
- Animované statistiky a metriky
- Kontaktní formulář s validací

## 🚀 Performance

- **Lazy loading** komponent a obrázků
- **Code splitting** pro optimální načítání
- **SCSS optimalizace** s kritickými styly
- **Gzip komprese** v Nginx
- **Caching strategie** pro statické assety

## 📈 SEO & Accessibility

- Semantic HTML struktura
- Meta tagy a Open Graph
- ARIA labels pro screen readery
- Keyboard navigation
- Kontrastní barvy pro přístupnost

## 🔒 Bezpečnost

- Security headers v Nginx
- Content Security Policy
- XSS ochrana
- HTTPS ready konfigurace

## 📄 Licence

Tento projekt je licencován pod MIT licencí.

## 🤝 Přispívání

1. Fork repozitáře
2. Vytvoř feature branch (`git checkout -b feature/amazing-feature`)
3. Commit změny (`git commit -m 'Add amazing feature'`)
4. Push do branch (`git push origin feature/amazing-feature`)
5. Otevři Pull Request

## 📞 Kontakt

**IoTVision Team**
- Email: <EMAIL>
- Web: https://iotvision.com

## 🆕 Aktuální Změny (Latest Updates)

### ✅ Dokončené Funkce
- **🌍 Kompletní Lokalizace**: Automatická detekce jazyka (čeština/angličtina)
- **🐳 Docker Optimalizace**: Opraveny version issues, optimalizované buildy
- **🎨 Profesionální UI**: Kompletní redesign s moderními komponentami
- **📧 Kontaktní Systém**: Funkční formulář s validací a zpětnou vazbou
- **⚡ Výkonnostní Optimalizace**: Nginx s cachingem a kompresí
- **🔒 Bezpečnost**: Security headers a best practices
- **📱 Responzivní Design**: Mobile-first přístup napříč všemi komponentami

### 🎯 Technické Vylepšení
- **i18n Integrace**: Kompletní překladový systém s automatickou detekcí
- **Komponentová Architektura**: Modulární, znovupoužitelné komponenty
- **SCSS Organizace**: Strukturované styly s proměnnými
- **Docker Multi-stage**: Optimalizované buildy a bezpečnost
- **Nginx Konfigurace**: Výkonnostní a bezpečnostní optimalizace

### 🌐 Dostupnost
- **Frontend**: http://localhost:80 (Docker)
- **Status**: ✅ Běží v produkčním módu
- **Health Check**: ✅ Aktivní monitoring

---

**IoTVision** - Transforming the future with cutting-edge IoT solutions 🚀

Vytvořeno s ❤️ pomocí Vue.js 3 a moderních web technologií.
















---

**IoTVision** - Transformujeme budoucnost pomocí špičkových IoT řešení 🚀

Vytvořeno s ❤️ pomocí Vue.js 3 a moderních webových technologií.
