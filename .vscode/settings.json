{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.tabSize": 2, "editor.insertSpaces": true, "files.eol": "\n", "typescript.preferences.importModuleSpecifier": "relative", "vue.codeActions.enabled": true, "vue.complete.casing.tags": "kebab", "vue.complete.casing.props": "camel", "emmet.includeLanguages": {"vue-html": "html"}, "files.associations": {"*.vue": "vue"}, "css.validate": false, "scss.validate": false, "less.validate": false, "stylelint.validate": ["css", "scss", "vue"], "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/node_modules": true}}