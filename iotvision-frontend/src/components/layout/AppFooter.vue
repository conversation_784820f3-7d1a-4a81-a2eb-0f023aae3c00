<template>
  <footer class="footer">
    <div class="container">
      <div class="footer__content">
        <!-- Logo and Description -->
        <div class="footer__brand">
          <div class="footer__logo">
            <span class="logo-text">IoT</span>
            <span class="logo-accent">Vision</span>
          </div>
          <p class="footer__description">
            {{ $t('footer.description') }}
          </p>
          <div class="footer__social">
            <a href="#" class="social-link" aria-label="LinkedIn">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            <a href="#" class="social-link" aria-label="Twitter">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
            <a href="#" class="social-link" aria-label="GitHub">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>
            <a href="#" class="social-link" aria-label="Telegram">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="footer__links">
          <h4 class="footer__title">{{ $t('footer.quickLinks') }}</h4>
          <ul class="footer__list">
            <li><RouterLink to="/" class="footer__link">{{ $t('nav.home') }}</RouterLink></li>
            <li><RouterLink to="/solutions" class="footer__link">{{ $t('nav.solutions') }}</RouterLink></li>
            <li><RouterLink to="/technologies" class="footer__link">{{ $t('nav.technologies') }}</RouterLink></li>
            <li><RouterLink to="/about" class="footer__link">{{ $t('nav.about') }}</RouterLink></li>
            <li><RouterLink to="/contact" class="footer__link">{{ $t('nav.contact') }}</RouterLink></li>
          </ul>
        </div>

        <!-- Services -->
        <div class="footer__links">
          <h4 class="footer__title">{{ $t('footer.solutions') }}</h4>
          <ul class="footer__list">
            <li><a href="#" class="footer__link">{{ $t('footer.services.iotDevelopment') }}</a></li>
            <li><a href="#" class="footer__link">{{ $t('footer.services.smartHomeSolutions') }}</a></li>
            <li><a href="#" class="footer__link">{{ $t('footer.services.hardwareDesign') }}</a></li>
            <li><a href="#" class="footer__link">{{ $t('footer.services.cloudIntegration') }}</a></li>
            <li><a href="#" class="footer__link">{{ $t('footer.services.consulting') }}</a></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="footer__contact">
          <h4 class="footer__title">{{ $t('nav.contact') }}</h4>
          <div class="contact-item">
            <span class="contact-label">Email:</span>
            <a href="mailto:<EMAIL>" class="contact-value">{{ $t('contact.info.email') }}</a>
          </div>
          <div class="contact-item">
            <span class="contact-label">Phone:</span>
            <a href="tel:+420123456789" class="contact-value">{{ $t('contact.info.phone') }}</a>
          </div>
          <div class="contact-item">
            <span class="contact-label">Address:</span>
            <span class="contact-value">{{ $t('contact.info.address') }}</span>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="footer__bottom">
        <div class="footer__copyright">
          <p>{{ $t('footer.copyright') }}</p>
        </div>

        <!-- Language Switcher -->
        <div class="footer__language">
          <div class="language-switcher">
            <button
              class="language-btn"
              :class="{ 'active': $i18n.locale === 'cs' }"
              @click="switchLanguage('cs')"
            >
              CS
            </button>
            <button
              class="language-btn"
              :class="{ 'active': $i18n.locale === 'en' }"
              @click="switchLanguage('en')"
            >
              EN
            </button>
          </div>
        </div>

        <div class="footer__legal">
          <a href="#" class="footer__link">{{ $t('footer.privacy') }}</a>
          <a href="#" class="footer__link">{{ $t('footer.terms') }}</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { RouterLink } from 'vue-router'
import { useI18n } from 'vue-i18n'

const currentYear = computed(() => new Date().getFullYear())
const { locale } = useI18n()

const switchLanguage = (lang: string) => {
  locale.value = lang
  localStorage.setItem('preferred-language', lang)
}
</script>

<style scoped lang="scss">

.footer {
  background: linear-gradient(135deg, $color-dark-blue 0%, $color-primary-bg 100%);
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  margin-top: auto;
  position: relative;
  z-index: 10;
  width: 100%;
  display: block;
  visibility: visible;
  min-height: 200px;
}

.footer__content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 3rem;
  padding: 3rem 0;

  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  @media (max-width: $breakpoint-md) {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

.footer__brand {
  .footer__logo {
    font-family: $font-heading;
    font-size: 1.8rem;
    font-weight: 900;
    margin-bottom: 1rem;

    .logo-text {
      color: $color-text-white;
      margin-right: 0.2rem;
    }

    .logo-accent {
      background: $gradient-text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

.footer__description {
  color: $color-text-muted;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  max-width: 300px;

  @media (max-width: $breakpoint-md) {
    max-width: none;
  }
}

.footer__social {
  display: flex;
  gap: 1rem;

  @media (max-width: $breakpoint-md) {
    justify-content: center;
  }
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-md;
  color: $color-neon-aqua;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: $color-neon-aqua;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
  }
}

.footer__title {
  color: $color-text-white;
  font-family: $font-heading;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 30px;
    height: 2px;
    background: $gradient-neon;
    border-radius: 1px;

    @media (max-width: $breakpoint-md) {
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

.footer__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__link {
  color: $color-text-muted;
  text-decoration: none;
  line-height: 2;
  transition: color 0.3s ease;

  &:hover {
    color: $color-neon-aqua;
  }
}

.footer__contact {
  .contact-item {
    margin-bottom: 0.8rem;
    
    .contact-label {
      color: $color-text-light;
      font-weight: 500;
      display: block;
      margin-bottom: 0.2rem;
    }
    
    .contact-value {
      color: $color-text-muted;
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: $color-neon-aqua;
      }
    }
  }
}

.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
  border-top: 1px solid rgba(0, 255, 255, 0.1);

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

.footer__copyright {
  color: $color-text-muted;
  font-size: 0.9rem;
}

.footer__legal {
  display: flex;
  gap: 2rem;

  .footer__link {
    font-size: 0.9rem;
  }
}

.footer__language {
  .language-switcher {
    display: flex;
    background: rgba(26, 26, 46, 0.8);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: $border-radius-md;
    overflow: hidden;
  }

  .language-btn {
    background: transparent;
    border: none;
    color: $color-text-muted;
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      color: $color-neon-aqua;
      background: rgba(0, 255, 255, 0.1);
    }

    &.active {
      color: $color-primary-bg;
      background: $color-neon-aqua;
    }
  }
}
</style>
