<template>
  <section class="technologies-preview section">
    <div class="container">
      <div class="technologies__header">
        <h2 class="technologies__title">
          {{ $t('technologies.title') }}
        </h2>
        <p class="technologies__subtitle">
          {{ $t('technologies.subtitle') }}
        </p>
      </div>
      
      <div class="technologies__content">
        <div class="tech-showcase">
          <div class="tech-visual">
            <canvas ref="techCanvasRef" class="tech-canvas"></canvas>
            <div class="tech-nodes">
              <div 
                v-for="(node, index) in techNodes" 
                :key="node.id"
                class="tech-node"
                :class="{ 'active': activeNode === index }"
                :style="{ 
                  left: node.x + '%', 
                  top: node.y + '%',
                  animationDelay: `${index * 0.2}s`
                }"
                @click="setActiveNode(index)"
              >
                <div class="node-icon">{{ node.icon }}</div>
                <div class="node-label">{{ $t(node.label) }}</div>
              </div>
            </div>
          </div>
          
          <div class="tech-details">
            <div class="tech-card" v-if="activeTech">
              <h3 class="tech-card__title">{{ $t(activeTech.titleKey) }}</h3>
              <p class="tech-card__description">{{ $t(activeTech.descriptionKey) }}</p>
              
              <div class="tech-specs">
                <div 
                  v-for="spec in activeTech.specs" 
                  :key="spec.label"
                  class="spec-item"
                >
                  <span class="spec-label">{{ spec.label }}</span>
                  <span class="spec-value">{{ spec.value }}</span>
                </div>
              </div>
              
              <div class="tech-features">
                <h4 class="features-title">{{ $t('technologies.keyFeatures') }}</h4>
                <ul class="features-list">
                  <li v-for="feature in activeTech.features" :key="feature">
                    {{ $t(feature) }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div class="tech-stats">
          <div class="stat-card">
            <div class="stat-number">15+</div>
            <div class="stat-label">{{ $t('technologies.stats.technologies') }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">99.9%</div>
            <div class="stat-label">{{ $t('technologies.stats.reliability') }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">24/7</div>
            <div class="stat-label">{{ $t('technologies.stats.support') }}</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

const techCanvasRef = ref<HTMLCanvasElement | null>(null)
const activeNode = ref(0)

const techNodes = [
  { id: 1, x: 20, y: 30, icon: '🔗', label: 'technologies.connectivity.name' },
  { id: 2, x: 50, y: 20, icon: '🧠', label: 'technologies.aiAnalytics.name' },
  { id: 3, x: 80, y: 35, icon: '☁️', label: 'technologies.cloud.title' },
  { id: 4, x: 30, y: 70, icon: '🔒', label: 'technologies.security.name' },
  { id: 5, x: 70, y: 75, icon: '📊', label: 'technologies.ai.title' }
]

const technologies = [
  {
    titleKey: 'technologies.iot.title',
    descriptionKey: 'technologies.iot.description',
    specs: [
      { label: 'Protocols', value: '15+' },
      { label: 'Range', value: '10km+' },
      { label: 'Latency', value: '<1ms' }
    ],
    features: [
      'technologies.iot.features.autoDiscovery',
      'technologies.iot.features.meshNetworking',
      'technologies.iot.features.adaptiveProtocol',
      'technologies.iot.features.edgeToCloud'
    ]
  },
  {
    titleKey: 'technologies.ai.title',
    descriptionKey: 'technologies.ai.description',
    specs: [
      { label: 'Models', value: '50+' },
      { label: 'Accuracy', value: '99.5%' },
      { label: 'Processing', value: 'Real-time' }
    ],
    features: [
      'technologies.ai.features.predictiveAnalytics',
      'technologies.ai.features.anomalyDetection',
      'technologies.ai.features.nlp',
      'technologies.ai.features.computerVision'
    ]
  },
  {
    titleKey: 'technologies.cloud.title',
    descriptionKey: 'technologies.cloud.description',
    specs: [
      { label: 'Uptime', value: '99.99%' },
      { label: 'Regions', value: '25+' },
      { label: 'Scale', value: 'Unlimited' }
    ],
    features: [
      'technologies.cloud.features.autoScaling',
      'technologies.cloud.features.globalEdge',
      'technologies.cloud.features.multiCloud',
      'technologies.cloud.features.disasterRecovery'
    ]
  },
  {
    titleKey: 'technologies.security.title',
    descriptionKey: 'technologies.security.description',
    specs: [
      { label: 'Encryption', value: 'AES-256' },
      { label: 'Compliance', value: 'SOC2, ISO27001' },
      { label: 'Authentication', value: 'Multi-factor' }
    ],
    features: [
      'technologies.security.features.zeroTrust',
      'technologies.security.features.blockchain',
      'technologies.security.features.threatIntelligence',
      'technologies.security.features.automatedUpdates'
    ]
  }
]

const activeTech = computed(() => technologies[activeNode.value])

const setActiveNode = (index: number) => {
  activeNode.value = index
}

let animationId: number | null = null

const initTechAnimation = () => {
  if (!techCanvasRef.value) return
  
  const canvas = techCanvasRef.value
  const ctx = canvas.getContext('2d')!
  
  const resizeCanvas = () => {
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight
  }
  
  resizeCanvas()
  window.addEventListener('resize', resizeCanvas)
  
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    // Draw connecting lines between nodes
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.3)'
    ctx.lineWidth = 1
    
    for (let i = 0; i < techNodes.length; i++) {
      for (let j = i + 1; j < techNodes.length; j++) {
        const node1 = techNodes[i]
        const node2 = techNodes[j]
        
        const x1 = (node1.x / 100) * canvas.width
        const y1 = (node1.y / 100) * canvas.height
        const x2 = (node2.x / 100) * canvas.width
        const y2 = (node2.y / 100) * canvas.height
        
        ctx.beginPath()
        ctx.moveTo(x1, y1)
        ctx.lineTo(x2, y2)
        ctx.stroke()
      }
    }
    
    animationId = requestAnimationFrame(animate)
  }
  
  animate()
}

onMounted(() => {
  setTimeout(() => {
    initTechAnimation()
  }, 500)
  
  // Auto-cycle through technologies
  const interval = setInterval(() => {
    activeNode.value = (activeNode.value + 1) % technologies.length
  }, 5000)
  
  onUnmounted(() => {
    clearInterval(interval)
    if (animationId) {
      cancelAnimationFrame(animationId)
    }
  })
})
</script>

<style scoped lang="scss">

.technologies-preview {
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.9) 0%, rgba(26, 26, 46, 0.6) 100%);
  position: relative;
}

.technologies__header {
  text-align: center;
  margin-bottom: 4rem;
  
  .technologies__title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    
    @media (max-width: $breakpoint-md) {
      font-size: 2rem;
    }
  }
  
  .technologies__subtitle {
    font-size: 1.2rem;
    color: $color-text-muted;
    max-width: 600px;
    margin: 0 auto;
  }
}

.tech-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  
  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.tech-visual {
  position: relative;
  height: 400px;
  background: rgba(26, 26, 46, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  overflow: hidden;
}

.tech-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.tech-nodes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.tech-node {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.3s ease;
  
  .node-icon {
    width: 50px;
    height: 50px;
    background: rgba(0, 255, 255, 0.2);
    border: 2px solid $color-neon-aqua;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
  }
  
  .node-label {
    font-size: 0.8rem;
    color: $color-text-muted;
    text-align: center;
    white-space: nowrap;
  }
  
  &.active,
  &:hover {
    .node-icon {
      background: rgba(0, 255, 255, 0.4);
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      transform: scale(1.1);
    }
    
    .node-label {
      color: $color-neon-aqua;
    }
  }
}

.tech-details {
  .tech-card {
    background: rgba(26, 26, 46, 0.6);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: $border-radius-lg;
    padding: 2rem;
    backdrop-filter: blur(10px);
    
    &__title {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: $color-text-white;
    }
    
    &__description {
      color: $color-text-muted;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }
  }
}

.tech-specs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
  
  .spec-item {
    text-align: center;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.1);
    border-radius: $border-radius-md;
    
    .spec-label {
      display: block;
      font-size: 0.8rem;
      color: $color-text-muted;
      margin-bottom: 0.5rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    .spec-value {
      font-family: $font-heading;
      font-size: 1.2rem;
      font-weight: 700;
      color: $color-neon-aqua;
    }
  }
}

.tech-features {
  .features-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: $color-text-white;
  }
  
  .features-list {
    list-style: none;
    
    li {
      position: relative;
      padding-left: 1.5rem;
      margin-bottom: 0.5rem;
      color: $color-text-muted;
      
      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: $color-neon-green;
        font-weight: bold;
      }
    }
  }
}

.tech-stats {
  display: flex;
  justify-content: center;
  align-items: stretch; // Ensure all cards have same height
  gap: 2rem;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: center;
  }
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: rgba(26, 26, 46, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  min-width: 150px;
  min-height: 120px; // Consistent height
  display: flex;
  flex-direction: column;
  justify-content: center;

  .stat-number {
    font-family: $font-heading;
    font-size: 2rem;
    font-weight: 700;
    color: $color-neon-aqua;
    text-shadow: 0 0 10px currentColor;
    margin-bottom: 0.5rem;
    line-height: 1; // Consistent line height
  }

  .stat-label {
    font-size: 0.9rem;
    color: $color-text-muted;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.2; // Consistent line height
  }
}
</style>
