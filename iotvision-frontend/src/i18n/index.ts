import { createI18n } from 'vue-i18n'

// Import language files
import en from './locales/en.json'
import cs from './locales/cs.json'

// Detect language preference with Czech as default
const getLanguage = (): string => {
  // First check localStorage for saved preference
  const savedLang = localStorage.getItem('preferred-language')
  if (savedLang && ['en', 'cs'].includes(savedLang)) {
    return savedLang
  }

  // Then check browser language
  const browserLang = navigator.language || (navigator as any).userLanguage

  // Check if browser language starts with 'en' (English)
  if (browserLang.toLowerCase().startsWith('en')) {
    return 'en'
  }

  // Default to Czech for all other languages (including Czech)
  return 'cs'
}

const i18n = createI18n({
  legacy: false,
  locale: getLanguage(),
  fallbackLocale: 'cs',
  globalInjection: true,
  messages: {
    en,
    cs
  }
})

export default i18n
