{"nav": {"home": "Home", "solutions": "Solutions", "technologies": "Technologies", "about": "About", "contact": "Contact", "getStarted": "Get Started", "switchLanguage": "Switch Language"}, "hero": {"badge": "Next Generation IoT", "title": "Welcome to the Future of Connected Intelligence", "titleHighlight": "Future", "subtitle": "Transform your world with cutting-edge IoT solutions that bridge the gap between imagination and reality. Where every device becomes intelligent, every connection meaningful, and every data point a step toward tomorrow.", "exploreSolutions": "Explore Solutions", "watchDemo": "Watch Demo", "scrollToExplore": "Scroll to explore", "stats": {"projects": "Projects Delivered", "devices": "Devices Connected", "uptime": "Uptime Guaranteed"}}, "features": {"title": "Revolutionary IoT Solutions", "subtitle": "Discover how our advanced technology transforms industries and creates intelligent ecosystems", "smartConnectivity": {"title": "Smart Connectivity", "description": "Seamless device integration with ultra-low latency and 99.9% reliability"}, "realTimeAnalytics": {"title": "Real-time Analytics", "description": "Advanced AI-powered insights that turn data into actionable intelligence"}, "secureInfrastructure": {"title": "Secure Infrastructure", "description": "Enterprise-grade security with end-to-end encryption and threat protection"}, "scalablePlatform": {"title": "Scalable Platform", "description": "Cloud-native architecture that grows with your business needs"}}, "technologies": {"title": "Cutting-Edge Technologies", "subtitle": "Powered by the latest innovations in IoT, AI, and cloud computing", "benefits": "Benefits", "keyFeatures": "Key Features", "technicalSpecs": "Technical Specifications", "connectivity": {"name": "Smart Connectivity", "description": "Advanced connectivity solutions for seamless device integration", "benefits": {"multiProtocol": "Multi-protocol support", "extendedRange": "Extended range connectivity", "lowLatency": "Ultra-low latency", "failover": "Automatic failover"}, "stats": {"protocols": "Protocols", "range": "Range", "latency": "Latency"}, "features": {"meshNetworking": {"name": "Mesh Networking", "description": "Self-healing network topology that automatically routes around failures", "specs": {"0": "Auto-discovery", "1": "Load balancing", "2": "Redundancy"}}, "edgeComputing": {"name": "Edge Computing", "description": "Process data locally to reduce latency and bandwidth usage", "specs": {"0": "Real-time processing", "1": "Local storage", "2": "Offline capability"}}}}, "aiAnalytics": {"name": "AI Analytics", "description": "Machine learning algorithms for predictive analytics and insights", "benefits": {"anomalyDetection": "Anomaly detection", "predictiveMaintenance": "Predictive maintenance", "automatedDecision": "Automated decision making", "patternRecognition": "Pattern recognition"}, "stats": {"models": "AI Models", "accuracy": "Accuracy", "processing": "Processing"}, "features": {"machineLearning": {"name": "Machine Learning", "description": "Advanced ML models for pattern recognition and prediction", "specs": {"0": "Deep learning", "1": "Neural networks", "2": "Auto-training"}}, "dataAnalytics": {"name": "Data Analytics", "description": "Comprehensive analytics platform for business intelligence", "specs": {"0": "Real-time dashboards", "1": "Custom reports", "2": "API access"}}}}, "security": {"name": "Security", "description": "Enterprise-grade security with end-to-end encryption", "benefits": {"encryption": "End-to-end encryption", "zeroTrust": "Zero-trust architecture", "compliance": "Compliance ready", "mfa": "Multi-factor authentication"}, "stats": {"encryption": "Encryption", "compliance": "Compliance", "uptime": "Uptime"}, "features": {"encryption": {"name": "Encryption", "description": "Military-grade encryption for all data transmission", "specs": {"0": "AES-256", "1": "TLS 1.3", "2": "Key rotation"}}, "accessControl": {"name": "Access Control", "description": "Granular permissions and role-based access control", "specs": {"0": "RBAC", "1": "SSO", "2": "MFA"}}, "zeroTrust": "Zero-trust architecture", "blockchain": "Blockchain verification", "threatIntelligence": "Threat intelligence", "automatedUpdates": "Automated security updates"}, "title": "Cybersecurity"}, "iot": {"title": "IoT Ecosystem", "description": "Complete device management and orchestration platform", "features": {"autoDiscovery": "Auto-discovery and pairing", "meshNetworking": "Mesh networking capabilities", "adaptiveProtocol": "Adaptive protocol switching", "edgeToCloud": "Edge-to-cloud connectivity"}}, "ai": {"title": "Artificial Intelligence", "description": "Machine learning algorithms for predictive analytics", "features": {"predictiveAnalytics": "Predictive analytics", "anomalyDetection": "Anomaly detection", "nlp": "Natural language processing", "computerVision": "Computer vision integration"}}, "cloud": {"title": "Cloud Infrastructure", "description": "Scalable and reliable cloud-native solutions", "features": {"autoScaling": "Auto-scaling infrastructure", "globalEdge": "Global edge deployment", "multiCloud": "Multi-cloud support", "disasterRecovery": "Disaster recovery"}}, "stats": {"technologies": "Technologies", "reliability": "Reliability", "support": "Support"}}, "solutions": {"title": "Industry Solutions", "subtitle": "Tailored IoT solutions for every industry vertical", "keyFeatures": "Key Features", "smartCities": {"title": "Smart Cities", "description": "Transform urban infrastructure with intelligent monitoring, traffic optimization, and energy management systems.", "features": ["Traffic Flow Optimization", "Smart Lighting Systems", "Waste Management", "Environmental Monitoring"], "statLabel": "Energy Savings"}, "manufacturing": {"title": "Industrial Manufacturing", "description": "Revolutionize production with predictive maintenance, quality control, and supply chain optimization.", "features": ["Predictive Maintenance", "Quality Control Automation", "Energy Optimization", "Supply Chain Visibility"], "statLabel": "Efficiency Increase"}, "healthcare": {"title": "Healthcare & Medical", "description": "Enhance patient care with remote monitoring, asset tracking, and intelligent medical devices.", "features": ["Patient Monitoring", "Asset Tracking", "Environmental Control", "Emergency Response"], "statLabel": "Response Time Improvement"}, "agriculture": {"title": "Smart Agriculture", "description": "Optimize crop yields with precision farming, soil monitoring, and automated irrigation systems.", "features": ["Soil Monitoring", "Automated Irrigation", "Crop Health Tracking", "Weather Integration"], "statLabel": "Yield Increase"}}, "about": {"title": "About IoTVision", "subtitle": "Leading the future of connected intelligence", "story": {"title": "Our Story", "paragraph1": "Founded in 2020 by a team of visionary engineers and data scientists, IoTVision emerged from a simple yet powerful belief: that the future belongs to those who can seamlessly connect the physical and digital worlds.", "paragraph2": "Today, we're proud to be at the forefront of the IoT revolution, helping businesses across industries transform their operations through intelligent connectivity and data-driven insights."}, "stats": {"projectsCompleted": "Projects Completed", "devicesConnected": "Devices Connected", "countriesServed": "Countries Served"}, "mission": {"title": "Our Mission", "description": "To democratize IoT technology and make intelligent connectivity accessible to businesses of all sizes, driving innovation and efficiency across industries."}, "vision": {"title": "Our Vision", "description": "A world where every device is intelligent, every connection is meaningful, and every data point contributes to a smarter, more sustainable future."}, "values": {"title": "Our Values", "description": "Innovation, reliability, and customer success drive everything we do. We believe in building lasting partnerships and delivering exceptional value."}, "team": {"title": "Meet Our Team", "members": {"alexChen": {"name": "<PERSON>", "role": "CEO & Co-Founder", "bio": "Visionary leader with 15+ years in IoT and enterprise technology."}, "sarahJohnson": {"name": "<PERSON>", "role": "CTO & Co-Founder", "bio": "Technical architect specializing in scalable IoT infrastructure."}, "michaelRodriguez": {"name": "<PERSON>", "role": "Head of AI/ML", "bio": "AI researcher focused on intelligent edge computing solutions."}, "emilyZhang": {"name": "<PERSON>", "role": "Head of Security", "bio": "Cybersecurity expert ensuring enterprise-grade protection."}}}}, "contact": {"title": "Get in Touch", "subtitle": "Ready to transform your business with IoT? Let's discuss your project.", "form": {"name": "Full Name", "email": "Email Address", "company": "Company Name", "phone": "Phone Number", "subject": "Subject", "message": "Message", "send": "Send Message", "sending": "Sending...", "success": "Message sent successfully!", "error": "Failed to send message. Please try again."}, "info": {"address": "Prague, Czech Republic", "email": "info[at]iotvision.com", "phone": "+*********** 789", "hours": "Mon-Fri: 9:00 - 18:00"}, "responseTime": {"title": "Response Time", "average": "Average Response", "emergency": "Emergency Support"}}, "cta": {"title": "Ready to Start Your IoT Journey?", "subtitle": "Join thousands of businesses already transforming with our IoT solutions", "button": "Get Started Today", "contact": "Contact Us", "features": {"consultation": "Free Consultation", "trial": "30-Day Trial", "support": "24/7 Support"}}, "common": {"learnMore": "Learn More"}, "stats": {"latency": "Latency", "accuracy": "Accuracy", "encryption": "Encryption", "devices": "Devices"}, "footer": {"description": "Leading provider of innovative IoT solutions for the connected world.", "quickLinks": "Quick Links", "solutions": "Solutions", "services": {"iotDevelopment": "IoT Development", "smartHomeSolutions": "Smart Home Solutions", "hardwareDesign": "Hardware Design", "cloudIntegration": "Cloud Integration", "consulting": "Consulting"}, "company": "Company", "legal": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>", "copyright": "© 2024 IoTVision. All rights reserved.", "madeWith": "Made with love using Vue.js 3 and modern web technologies."}}