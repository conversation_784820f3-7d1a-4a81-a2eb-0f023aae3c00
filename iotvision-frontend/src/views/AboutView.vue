<template>
  <div class="about-view">
    <section class="about-hero section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('about.title') }}
          </h1>
          <p class="hero-subtitle">
            {{ $t('about.subtitle') }}
          </p>
        </div>
      </div>
    </section>

    <section class="about-story section">
      <div class="container">
        <div class="story-content">
          <div class="story-text">
            <h2>{{ $t('about.story.title') }}</h2>
            <p>
              {{ $t('about.story.paragraph1') }}
            </p>
            <p>
              {{ $t('about.story.paragraph2') }}
            </p>
          </div>

          <div class="story-stats">
            <div class="stat-card">
              <div class="stat-number">500+</div>
              <div class="stat-label">{{ $t('about.stats.projectsCompleted') }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">50M+</div>
              <div class="stat-label">{{ $t('about.stats.devicesConnected') }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">25+</div>
              <div class="stat-label">{{ $t('about.stats.countriesServed') }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="about-mission section">
      <div class="container">
        <div class="mission-grid">
          <div class="mission-item">
            <div class="mission-icon">🎯</div>
            <h3>{{ $t('about.mission.title') }}</h3>
            <p>
              {{ $t('about.mission.description') }}
            </p>
          </div>

          <div class="mission-item">
            <div class="mission-icon">👁️</div>
            <h3>{{ $t('about.vision.title') }}</h3>
            <p>
              {{ $t('about.vision.description') }}
            </p>
          </div>

          <div class="mission-item">
            <div class="mission-icon">⚡</div>
            <h3>{{ $t('about.values.title') }}</h3>
            <p>
              {{ $t('about.values.description') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="about-team section">
      <div class="container">
        <h2 class="section-title">{{ $t('about.team.title') }}</h2>
        <div class="team-grid">
          <div v-for="member in teamMembers" :key="member.id" class="team-card">
            <div class="member-avatar">{{ member.avatar }}</div>
            <h3 class="member-name">{{ $t(member.nameKey) }}</h3>
            <p class="member-role">{{ $t(member.roleKey) }}</p>
            <p class="member-bio">{{ $t(member.bioKey) }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
const teamMembers = [
  {
    id: 1,
    nameKey: 'about.team.members.alexChen.name',
    roleKey: 'about.team.members.alexChen.role',
    avatar: '👨‍💼',
    bioKey: 'about.team.members.alexChen.bio'
  },
  {
    id: 2,
    nameKey: 'about.team.members.sarahJohnson.name',
    roleKey: 'about.team.members.sarahJohnson.role',
    avatar: '👩‍💻',
    bioKey: 'about.team.members.sarahJohnson.bio'
  },
  {
    id: 3,
    nameKey: 'about.team.members.michaelRodriguez.name',
    roleKey: 'about.team.members.michaelRodriguez.role',
    avatar: '👨‍🔬',
    bioKey: 'about.team.members.michaelRodriguez.bio'
  },
  {
    id: 4,
    nameKey: 'about.team.members.emilyZhang.name',
    roleKey: 'about.team.members.emilyZhang.role',
    avatar: '👩‍🔒',
    bioKey: 'about.team.members.emilyZhang.bio'
  }
]
</script>

<style scoped lang="scss">

.about-view {
  padding-top: 70px;
}

.about-hero {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(10, 10, 15, 0.9) 100%);
  text-align: center;
  padding: 4rem 0;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1.5rem;

  @media (max-width: $breakpoint-md) {
    font-size: 2.5rem;
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  color: $color-text-muted;
  line-height: 1.6;
}

.story-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: center;

  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.story-text {
  h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: $color-text-white;
  }

  p {
    color: $color-text-muted;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }
}

.story-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (max-width: $breakpoint-lg) {
    flex-direction: row;
    justify-content: center;
  }

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
  }
}

.stat-card {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 1.5rem;
  text-align: center;

  .stat-number {
    font-family: $font-heading;
    font-size: 2rem;
    font-weight: 700;
    color: $color-neon-aqua;
    text-shadow: 0 0 10px currentColor;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-size: 0.9rem;
    color: $color-text-muted;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.mission-item {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
  }

  .mission-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  h3 {
    color: $color-text-white;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }

  p {
    color: $color-text-muted;
    line-height: 1.6;
  }
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: $color-text-white;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.team-card {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
  }
}

.member-avatar {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.member-name {
  color: $color-text-white;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.member-role {
  color: $color-neon-aqua;
  margin-bottom: 1rem;
  font-weight: 500;
}

.member-bio {
  color: $color-text-muted;
  line-height: 1.5;
  font-size: 0.9rem;
}
</style>
