<template>
  <div class="technologies-view">
    <section class="tech-hero section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('technologies.title') }}
          </h1>
          <p class="hero-subtitle">
            {{ $t('technologies.subtitle') }}
          </p>
        </div>
      </div>
    </section>

    <section class="tech-details section">
      <div class="container">
        <div class="tech-tabs">
          <button 
            v-for="(tech, index) in technologies" 
            :key="tech.id"
            class="tab-button"
            :class="{ 'active': activeTab === index }"
            @click="activeTab = index"
          >
            {{ tech.name }}
          </button>
        </div>
        
        <div class="tech-content" v-if="activeTechnology">
          <div class="tech-overview">
            <div class="tech-info">
              <h2 class="tech-title">{{ $t(activeTechnology.name) }}</h2>
              <p class="tech-description">{{ $t(activeTechnology.description) }}</p>
              
              <div class="tech-benefits">
                <h3>{{ $t('technologies.benefits') }}</h3>
                <ul>
                  <li v-for="benefit in activeTechnology.benefits" :key="benefit">
                    {{ $t(benefit) }}
                  </li>
                </ul>
              </div>
            </div>
            
            <div class="tech-visual">
              <div class="visual-placeholder">
                <div class="tech-icon">{{ activeTechnology.icon }}</div>
                <div class="tech-stats">
                  <div
                    v-for="stat in activeTechnology.stats"
                    :key="stat.label"
                    class="stat-item"
                  >
                    <span class="stat-value">{{ stat.value }}</span>
                    <span class="stat-label">{{ $t(stat.label) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="tech-features">
            <h3>Technical Specifications</h3>
            <div class="features-grid">
              <div 
                v-for="feature in activeTechnology.features" 
                :key="feature.name"
                class="feature-card"
              >
                <h4>{{ feature.name }}</h4>
                <p>{{ feature.description }}</p>
                <div class="feature-specs">
                  <span v-for="spec in feature.specs" :key="spec" class="spec-tag">
                    {{ spec }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const activeTab = ref(0)

const technologies = [
  {
    id: 1,
    name: 'technologies.connectivity.name',
    icon: '📡',
    description: 'technologies.connectivity.description',
    benefits: [
      'technologies.connectivity.benefits.multiProtocol',
      'technologies.connectivity.benefits.extendedRange',
      'technologies.connectivity.benefits.lowLatency',
      'technologies.connectivity.benefits.failover'
    ],
    stats: [
      { label: 'technologies.connectivity.stats.protocols', value: '15+' },
      { label: 'technologies.connectivity.stats.range', value: '10km' },
      { label: 'technologies.connectivity.stats.latency', value: '<1ms' }
    ],
    features: [
      {
        name: 'Mesh Networking',
        description: 'Self-healing network topology that automatically routes around failures',
        specs: ['Auto-discovery', 'Load balancing', 'Redundancy']
      },
      {
        name: 'Edge Computing',
        description: 'Process data locally to reduce latency and bandwidth usage',
        specs: ['Real-time processing', 'Local storage', 'Offline capability']
      }
    ]
  },
  {
    id: 2,
    name: 'technologies.aiAnalytics.name',
    icon: '🧠',
    description: 'technologies.aiAnalytics.description',
    benefits: [
      'technologies.aiAnalytics.benefits.anomalyDetection',
      'technologies.aiAnalytics.benefits.predictiveMaintenance',
      'technologies.aiAnalytics.benefits.automatedDecision',
      'technologies.aiAnalytics.benefits.patternRecognition'
    ],
    stats: [
      { label: 'technologies.aiAnalytics.stats.models', value: '50+' },
      { label: 'technologies.aiAnalytics.stats.accuracy', value: '99.5%' },
      { label: 'technologies.aiAnalytics.stats.processing', value: 'Real-time' }
    ],
    features: [
      {
        name: 'Machine Learning',
        description: 'Advanced ML models for pattern recognition and prediction',
        specs: ['Deep learning', 'Neural networks', 'Auto-training']
      },
      {
        name: 'Data Analytics',
        description: 'Comprehensive analytics platform for business intelligence',
        specs: ['Real-time dashboards', 'Custom reports', 'API access']
      }
    ]
  },
  {
    id: 3,
    name: 'technologies.security.name',
    icon: '🔒',
    description: 'technologies.security.description',
    benefits: [
      'technologies.security.benefits.encryption',
      'technologies.security.benefits.zeroTrust',
      'technologies.security.benefits.compliance',
      'technologies.security.benefits.mfa'
    ],
    stats: [
      { label: 'technologies.security.stats.encryption', value: 'AES-256' },
      { label: 'technologies.security.stats.compliance', value: 'SOC2' },
      { label: 'technologies.security.stats.uptime', value: '99.99%' }
    ],
    features: [
      {
        name: 'Encryption',
        description: 'Military-grade encryption for all data transmission',
        specs: ['AES-256', 'TLS 1.3', 'Key rotation']
      },
      {
        name: 'Access Control',
        description: 'Granular permissions and role-based access control',
        specs: ['RBAC', 'SSO', 'MFA']
      }
    ]
  }
]

const activeTechnology = computed(() => technologies[activeTab.value])
</script>

<style scoped lang="scss">

.technologies-view {
  padding-top: 70px;
}

.tech-hero {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(10, 10, 15, 0.9) 100%);
  text-align: center;
  padding: 4rem 0;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  
  @media (max-width: $breakpoint-md) {
    font-size: 2.5rem;
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  color: $color-text-muted;
  line-height: 1.6;
}

.tech-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  
  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: center;
  }
}

.tab-button {
  padding: 1rem 2rem;
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-md;
  color: $color-text-light;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    color: $color-neon-aqua;
  }
  
  &.active {
    background: rgba(0, 255, 255, 0.1);
    border-color: $color-neon-aqua;
    color: $color-neon-aqua;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  }
}

.tech-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  
  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
  }
}

.tech-info {
  .tech-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: $color-text-white;
  }
  
  .tech-description {
    color: $color-text-muted;
    line-height: 1.6;
    margin-bottom: 2rem;
  }
}

.tech-benefits {
  h3 {
    color: $color-text-white;
    margin-bottom: 1rem;
  }
  
  ul {
    list-style: none;
    
    li {
      position: relative;
      padding-left: 1.5rem;
      margin-bottom: 0.5rem;
      color: $color-text-muted;
      
      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: $color-neon-green;
        font-weight: bold;
      }
    }
  }
}

.tech-visual {
  .visual-placeholder {
    background: rgba(26, 26, 46, 0.6);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: $border-radius-lg;
    padding: 2rem;
    text-align: center;
    
    .tech-icon {
      font-size: 4rem;
      margin-bottom: 2rem;
    }
  }
}

.tech-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  
  .stat-item {
    text-align: center;
    
    .stat-value {
      display: block;
      font-family: $font-heading;
      font-size: 1.5rem;
      font-weight: 700;
      color: $color-neon-aqua;
      margin-bottom: 0.3rem;
    }
    
    .stat-label {
      font-size: 0.8rem;
      color: $color-text-muted;
      text-transform: uppercase;
    }
  }
}

.tech-features {
  h3 {
    color: $color-text-white;
    margin-bottom: 2rem;
    font-size: 1.5rem;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 1.5rem;
  
  h4 {
    color: $color-text-white;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: $color-text-muted;
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }
}

.feature-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  
  .spec-tag {
    background: rgba(0, 255, 255, 0.1);
    color: $color-neon-aqua;
    padding: 0.3rem 0.8rem;
    border-radius: $border-radius-sm;
    font-size: 0.8rem;
  }
}
</style>
