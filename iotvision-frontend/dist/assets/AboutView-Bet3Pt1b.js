import{d as r,k as i,l as s,t,F as d,y as m,p as n}from"./vendor-Cao0-QMi.js";import{_ as c}from"./index-CU5qiZLv.js";import"./i18n-laVgjn-G.js";const b={class:"about-view"},u={class:"about-hero section"},h={class:"container"},_={class:"hero-content"},v={class:"hero-title"},p={class:"hero-subtitle"},y={class:"about-story section"},$={class:"container"},K={class:"story-content"},g={class:"story-text"},f={class:"story-stats"},C={class:"stat-card"},k={class:"stat-label"},w={class:"stat-card"},z={class:"stat-label"},B={class:"stat-card"},J={class:"stat-label"},R={class:"about-mission section"},V={class:"container"},Z={class:"mission-grid"},A={class:"mission-item"},F={class:"mission-item"},M={class:"mission-item"},S={class:"about-team section"},j={class:"container"},D={class:"section-title"},E={class:"team-grid"},I={class:"member-avatar"},L={class:"member-name"},N={class:"member-role"},q={class:"member-bio"},G=r({__name:"AboutView",setup(H){const l=[{id:1,nameKey:"about.team.members.alexChen.name",roleKey:"about.team.members.alexChen.role",avatar:"👨‍💼",bioKey:"about.team.members.alexChen.bio"},{id:2,nameKey:"about.team.members.sarahJohnson.name",roleKey:"about.team.members.sarahJohnson.role",avatar:"👩‍💻",bioKey:"about.team.members.sarahJohnson.bio"},{id:3,nameKey:"about.team.members.michaelRodriguez.name",roleKey:"about.team.members.michaelRodriguez.role",avatar:"👨‍🔬",bioKey:"about.team.members.michaelRodriguez.bio"},{id:4,nameKey:"about.team.members.emilyZhang.name",roleKey:"about.team.members.emilyZhang.role",avatar:"👩‍🔒",bioKey:"about.team.members.emilyZhang.bio"}];return(e,o)=>(n(),i("div",b,[s("section",u,[s("div",h,[s("div",_,[s("h1",v,t(e.$t("about.title")),1),s("p",p,t(e.$t("about.subtitle")),1)])])]),s("section",y,[s("div",$,[s("div",K,[s("div",g,[s("h2",null,t(e.$t("about.story.title")),1),s("p",null,t(e.$t("about.story.paragraph1")),1),s("p",null,t(e.$t("about.story.paragraph2")),1)]),s("div",f,[s("div",C,[o[0]||(o[0]=s("div",{class:"stat-number"},"500+",-1)),s("div",k,t(e.$t("about.stats.projectsCompleted")),1)]),s("div",w,[o[1]||(o[1]=s("div",{class:"stat-number"},"50M+",-1)),s("div",z,t(e.$t("about.stats.devicesConnected")),1)]),s("div",B,[o[2]||(o[2]=s("div",{class:"stat-number"},"25+",-1)),s("div",J,t(e.$t("about.stats.countriesServed")),1)])])])])]),s("section",R,[s("div",V,[s("div",Z,[s("div",A,[o[3]||(o[3]=s("div",{class:"mission-icon"},"🎯",-1)),s("h3",null,t(e.$t("about.mission.title")),1),s("p",null,t(e.$t("about.mission.description")),1)]),s("div",F,[o[4]||(o[4]=s("div",{class:"mission-icon"},"👁️",-1)),s("h3",null,t(e.$t("about.vision.title")),1),s("p",null,t(e.$t("about.vision.description")),1)]),s("div",M,[o[5]||(o[5]=s("div",{class:"mission-icon"},"⚡",-1)),s("h3",null,t(e.$t("about.values.title")),1),s("p",null,t(e.$t("about.values.description")),1)])])])]),s("section",S,[s("div",j,[s("h2",D,t(e.$t("about.team.title")),1),s("div",E,[(n(),i(d,null,m(l,a=>s("div",{key:a.id,class:"team-card"},[s("div",I,t(a.avatar),1),s("h3",L,t(e.$t(a.nameKey)),1),s("p",N,t(e.$t(a.roleKey)),1),s("p",q,t(e.$t(a.bioKey)),1)])),64))])])])]))}}),T=c(G,[["__scopeId","data-v-b9b871ec"]]);export{T as default};
