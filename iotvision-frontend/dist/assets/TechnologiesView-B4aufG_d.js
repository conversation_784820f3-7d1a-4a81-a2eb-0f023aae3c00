import{d as y,r as d,c as g,k as n,l as e,t,A as p,F as l,y as r,n as v,p as c}from"./vendor-Cao0-QMi.js";import{_ as m}from"./index-iDtYrURv.js";import"./i18n-laVgjn-G.js";const f={class:"technologies-view"},_={class:"tech-hero section"},b={class:"container"},A={class:"hero-content"},k={class:"hero-title"},C={class:"hero-subtitle"},K={class:"tech-details section"},$={class:"container"},w={class:"tech-tabs"},L=["onClick"],N={key:0,class:"tech-content"},T={class:"tech-overview"},S={class:"tech-info"},V={class:"tech-title"},B={class:"tech-description"},D={class:"tech-benefits"},R={class:"tech-visual"},z={class:"visual-placeholder"},E={class:"tech-icon"},F={class:"tech-stats"},I={class:"stat-value"},M={class:"stat-label"},O={class:"tech-features"},P={class:"features-grid"},j={class:"feature-specs"},q=y({__name:"TechnologiesView",setup(G){const h=d(0),u=[{id:1,name:"technologies.connectivity.name",icon:"📡",description:"technologies.connectivity.description",benefits:["technologies.connectivity.benefits.multiProtocol","technologies.connectivity.benefits.extendedRange","technologies.connectivity.benefits.lowLatency","technologies.connectivity.benefits.failover"],stats:[{label:"technologies.connectivity.stats.protocols",value:"15+"},{label:"technologies.connectivity.stats.range",value:"10km"},{label:"technologies.connectivity.stats.latency",value:"<1ms"}],features:[{nameKey:"technologies.connectivity.features.meshNetworking.name",descriptionKey:"technologies.connectivity.features.meshNetworking.description",specs:["technologies.connectivity.features.meshNetworking.specs.0","technologies.connectivity.features.meshNetworking.specs.1","technologies.connectivity.features.meshNetworking.specs.2"]},{nameKey:"technologies.connectivity.features.edgeComputing.name",descriptionKey:"technologies.connectivity.features.edgeComputing.description",specs:["technologies.connectivity.features.edgeComputing.specs.0","technologies.connectivity.features.edgeComputing.specs.1","technologies.connectivity.features.edgeComputing.specs.2"]}]},{id:2,name:"technologies.aiAnalytics.name",icon:"🧠",description:"technologies.aiAnalytics.description",benefits:["technologies.aiAnalytics.benefits.anomalyDetection","technologies.aiAnalytics.benefits.predictiveMaintenance","technologies.aiAnalytics.benefits.automatedDecision","technologies.aiAnalytics.benefits.patternRecognition"],stats:[{label:"technologies.aiAnalytics.stats.models",value:"50+"},{label:"technologies.aiAnalytics.stats.accuracy",value:"99.5%"},{label:"technologies.aiAnalytics.stats.processing",value:"Real-time"}],features:[{nameKey:"technologies.aiAnalytics.features.machineLearning.name",descriptionKey:"technologies.aiAnalytics.features.machineLearning.description",specs:["technologies.aiAnalytics.features.machineLearning.specs.0","technologies.aiAnalytics.features.machineLearning.specs.1","technologies.aiAnalytics.features.machineLearning.specs.2"]},{nameKey:"technologies.aiAnalytics.features.dataAnalytics.name",descriptionKey:"technologies.aiAnalytics.features.dataAnalytics.description",specs:["technologies.aiAnalytics.features.dataAnalytics.specs.0","technologies.aiAnalytics.features.dataAnalytics.specs.1","technologies.aiAnalytics.features.dataAnalytics.specs.2"]}]},{id:3,name:"technologies.security.name",icon:"🔒",description:"technologies.security.description",benefits:["technologies.security.benefits.encryption","technologies.security.benefits.zeroTrust","technologies.security.benefits.compliance","technologies.security.benefits.mfa"],stats:[{label:"technologies.security.stats.encryption",value:"AES-256"},{label:"technologies.security.stats.compliance",value:"SOC2"},{label:"technologies.security.stats.uptime",value:"99.99%"}],features:[{nameKey:"technologies.security.features.encryption.name",descriptionKey:"technologies.security.features.encryption.description",specs:["technologies.security.features.encryption.specs.0","technologies.security.features.encryption.specs.1","technologies.security.features.encryption.specs.2"]},{nameKey:"technologies.security.features.accessControl.name",descriptionKey:"technologies.security.features.accessControl.description",specs:["technologies.security.features.accessControl.specs.0","technologies.security.features.accessControl.specs.1","technologies.security.features.accessControl.specs.2"]}]}],o=g(()=>u[h.value]);return(i,H)=>(c(),n("div",f,[e("section",_,[e("div",b,[e("div",A,[e("h1",k,t(i.$t("technologies.title")),1),e("p",C,t(i.$t("technologies.subtitle")),1)])])]),e("section",K,[e("div",$,[e("div",w,[(c(),n(l,null,r(u,(s,a)=>e("button",{key:s.id,class:v(["tab-button",{active:h.value===a}]),onClick:J=>h.value=a},t(s.name),11,L)),64))]),o.value?(c(),n("div",N,[e("div",T,[e("div",S,[e("h2",V,t(i.$t(o.value.name)),1),e("p",B,t(i.$t(o.value.description)),1),e("div",D,[e("h3",null,t(i.$t("technologies.benefits")),1),e("ul",null,[(c(!0),n(l,null,r(o.value.benefits,s=>(c(),n("li",{key:s},t(i.$t(s)),1))),128))])])]),e("div",R,[e("div",z,[e("div",E,t(o.value.icon),1),e("div",F,[(c(!0),n(l,null,r(o.value.stats,s=>(c(),n("div",{key:s.label,class:"stat-item"},[e("span",I,t(s.value),1),e("span",M,t(i.$t(s.label)),1)]))),128))])])])]),e("div",O,[e("h3",null,t(i.$t("technologies.technicalSpecs")),1),e("div",P,[(c(!0),n(l,null,r(o.value.features,s=>(c(),n("div",{key:s.nameKey,class:"feature-card"},[e("h4",null,t(i.$t(s.nameKey)),1),e("p",null,t(i.$t(s.descriptionKey)),1),e("div",j,[(c(!0),n(l,null,r(s.specs,a=>(c(),n("span",{key:a,class:"spec-tag"},t(i.$t(a)),1))),128))])]))),128))])])])):p("",!0)])])]))}}),X=m(q,[["__scopeId","data-v-98610e36"]]);export{X as default};
