import{d as c,k as i,l as t,t as s,F as n,y as u,j as d,m as h,q as _,u as f,R as m,p as a}from"./vendor-Cao0-QMi.js";import{_ as p}from"./index-CU5qiZLv.js";import"./i18n-laVgjn-G.js";const y={class:"solutions-view"},v={class:"solutions-hero section"},g={class:"container"},b={class:"hero-content"},K={class:"hero-title"},C={class:"hero-subtitle"},$={class:"solutions-grid section"},k={class:"container"},L={class:"solutions-list"},V={class:"solution-icon"},w={class:"solution-title"},B={class:"solution-description"},F={class:"solution-features"},N={class:"solution-stats"},S={class:"stat"},R={class:"stat-value"},j={class:"stat-label"},q=c({__name:"SolutionsView",setup(D){const r=[{id:1,titleKey:"solutions.manufacturing.title",icon:"🏭",descriptionKey:"solutions.manufacturing.description",features:["solutions.manufacturing.features.0","solutions.manufacturing.features.1","solutions.manufacturing.features.2","solutions.manufacturing.features.3"],stat:{value:"40%",labelKey:"solutions.manufacturing.statLabel"}},{id:2,titleKey:"solutions.smartCities.title",icon:"🏙️",descriptionKey:"solutions.smartCities.description",features:["solutions.smartCities.features.0","solutions.smartCities.features.1","solutions.smartCities.features.2","solutions.smartCities.features.3"],stat:{value:"30%",labelKey:"solutions.smartCities.statLabel"}},{id:3,titleKey:"solutions.healthcare.title",icon:"🏥",descriptionKey:"solutions.healthcare.description",features:["solutions.healthcare.features.0","solutions.healthcare.features.1","solutions.healthcare.features.2","solutions.healthcare.features.3"],stat:{value:"50%",labelKey:"solutions.healthcare.statLabel"}},{id:4,titleKey:"solutions.agriculture.title",icon:"🌾",descriptionKey:"solutions.agriculture.description",features:["solutions.agriculture.features.0","solutions.agriculture.features.1","solutions.agriculture.features.2","solutions.agriculture.features.3"],stat:{value:"25%",labelKey:"solutions.agriculture.statLabel"}}];return(e,E)=>(a(),i("div",y,[t("section",v,[t("div",g,[t("div",b,[t("h1",K,s(e.$t("solutions.title")),1),t("p",C,s(e.$t("solutions.subtitle")),1)])])]),t("section",$,[t("div",k,[t("div",L,[(a(),i(n,null,u(r,o=>t("div",{key:o.id,class:"solution-card"},[t("div",V,s(o.icon),1),t("h3",w,s(e.$t(o.titleKey)),1),t("p",B,s(e.$t(o.descriptionKey)),1),t("div",F,[t("h4",null,s(e.$t("solutions.keyFeatures"))+":",1),t("ul",null,[(a(!0),i(n,null,u(o.features,l=>(a(),i("li",{key:l},s(e.$t(l)),1))),128))])]),t("div",N,[t("div",S,[t("span",R,s(o.stat.value),1),t("span",j,s(e.$t(o.stat.labelKey)),1)])]),d(f(m),{to:"/contact",class:"btn btn-outline solution-cta"},{default:h(()=>[_(s(e.$t("common.learnMore")),1)]),_:1})])),64))])])])]))}}),z=p(q,[["__scopeId","data-v-91b00db1"]]);export{z as default};
