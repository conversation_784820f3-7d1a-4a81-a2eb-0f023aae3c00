const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/SolutionsView-MzyRlEEI.js","assets/vendor-Cao0-QMi.js","assets/i18n-laVgjn-G.js","assets/SolutionsView-DqDdkGfm.css","assets/TechnologiesView-B4aufG_d.js","assets/TechnologiesView-B7E63xxa.css","assets/AboutView-CrXYiasd.js","assets/AboutView-D0wPzQKv.css","assets/ContactView-BXSRbHo2.js","assets/ContactView-CR5BDmfO.css"])))=>i.map(i=>d[i]);
import{d as I,r as M,o as F,b as V,k as g,n as _,l as e,j as p,u as y,R as k,m as $,p as b,q as C,t as i,c as W,v as N,x as q,F as D,y as x,z as Z,A as G,B as Y,C as J,D as Q,E as X}from"./vendor-Cao0-QMi.js";import{u as ee,c as te}from"./i18n-laVgjn-G.js";(function(){const d=document.createElement("link").relList;if(d&&d.supports&&d.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))t(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const r of s.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&t(r)}).observe(document,{childList:!0,subtree:!0});function a(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function t(o){if(o.ep)return;o.ep=!0;const s=a(o);fetch(o.href,s)}})();const oe={class:"nav container"},se={class:"nav__logo"},ne={class:"nav__item"},ae={class:"nav__item"},ie={class:"nav__item"},le={class:"nav__item"},ce={class:"nav__item"},re={class:"nav__cta"},de=["aria-expanded"],ue=I({__name:"AppHeader",setup(m){const d=M(!1),a=M(!1),t=()=>{d.value=window.scrollY>50},o=()=>{a.value=!a.value},s=()=>{a.value=!1};return F(()=>{window.addEventListener("scroll",t)}),V(()=>{window.removeEventListener("scroll",t)}),(r,c)=>(b(),g("header",{class:_(["header",{"header--scrolled":d.value}])},[e("nav",oe,[e("div",se,[p(y(k),{to:"/",class:"logo-link"},{default:$(()=>c[0]||(c[0]=[e("span",{class:"logo-text"},"IoT",-1),e("span",{class:"logo-accent"},"Vision",-1)])),_:1,__:[0]})]),e("ul",{class:_(["nav__menu",{"nav__menu--open":a.value}])},[e("li",ne,[p(y(k),{to:"/",class:"nav__link",onClick:s},{default:$(()=>[C(i(r.$t("nav.home")),1)]),_:1})]),e("li",ae,[p(y(k),{to:"/solutions",class:"nav__link",onClick:s},{default:$(()=>[C(i(r.$t("nav.solutions")),1)]),_:1})]),e("li",ie,[p(y(k),{to:"/technologies",class:"nav__link",onClick:s},{default:$(()=>[C(i(r.$t("nav.technologies")),1)]),_:1})]),e("li",le,[p(y(k),{to:"/about",class:"nav__link",onClick:s},{default:$(()=>[C(i(r.$t("nav.about")),1)]),_:1})]),e("li",ce,[p(y(k),{to:"/contact",class:"nav__link",onClick:s},{default:$(()=>[C(i(r.$t("nav.contact")),1)]),_:1})])],2),e("div",re,[p(y(k),{to:"/contact",class:"btn btn-primary"},{default:$(()=>[C(i(r.$t("nav.getStarted")),1)]),_:1})]),e("button",{class:"nav__toggle",onClick:o,"aria-expanded":a.value,"aria-label":"Toggle navigation menu"},c[1]||(c[1]=[e("span",{class:"nav__toggle-line"},null,-1),e("span",{class:"nav__toggle-line"},null,-1),e("span",{class:"nav__toggle-line"},null,-1)]),8,de)])],2))}}),P=(m,d)=>{const a=m.__vccOpts||m;for(const[t,o]of d)a[t]=o;return a},ve=P(ue,[["__scopeId","data-v-de3f075c"]]),he={class:"footer"},pe={class:"container"},me={class:"footer__content"},fe={class:"footer__brand"},_e={class:"footer__description"},ge={class:"footer__links"},ye={class:"footer__title"},be={class:"footer__list"},ke={class:"footer__links"},$e={class:"footer__title"},we={class:"footer__contact"},ze={class:"footer__title"},Ce={class:"contact-item"},Te={href:"mailto:<EMAIL>",class:"contact-value"},Ie={class:"contact-item"},Pe={href:"tel:+420123456789",class:"contact-value"},Se={class:"contact-item"},Ae={class:"contact-value"},Me={class:"footer__bottom"},je={class:"footer__copyright"},Ee={class:"footer__language"},Le={class:"language-switcher"},Fe={class:"footer__legal"},Re={href:"#",class:"footer__link"},De={href:"#",class:"footer__link"},xe=I({__name:"AppFooter",setup(m){W(()=>new Date().getFullYear());const{locale:d}=ee(),a=t=>{d.value=t,localStorage.setItem("preferred-language",t)};return(t,o)=>(b(),g("footer",he,[e("div",pe,[e("div",me,[e("div",fe,[o[2]||(o[2]=e("div",{class:"footer__logo"},[e("span",{class:"logo-text"},"IoT"),e("span",{class:"logo-accent"},"Vision")],-1)),e("p",_e,i(t.$t("footer.description")),1),o[3]||(o[3]=N('<div class="footer__social" data-v-2f291a3e><a href="#" class="social-link" aria-label="LinkedIn" data-v-2f291a3e><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-2f291a3e><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" data-v-2f291a3e></path></svg></a><a href="#" class="social-link" aria-label="Twitter" data-v-2f291a3e><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-2f291a3e><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" data-v-2f291a3e></path></svg></a><a href="#" class="social-link" aria-label="GitHub" data-v-2f291a3e><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-2f291a3e><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" data-v-2f291a3e></path></svg></a><a href="#" class="social-link" aria-label="Telegram" data-v-2f291a3e><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-2f291a3e><path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" data-v-2f291a3e></path></svg></a></div>',1))]),e("div",ge,[e("h4",ye,i(t.$t("footer.quickLinks")),1),e("ul",be,[e("li",null,[p(y(k),{to:"/",class:"footer__link"},{default:$(()=>[C(i(t.$t("nav.home")),1)]),_:1})]),e("li",null,[p(y(k),{to:"/solutions",class:"footer__link"},{default:$(()=>[C(i(t.$t("nav.solutions")),1)]),_:1})]),e("li",null,[p(y(k),{to:"/technologies",class:"footer__link"},{default:$(()=>[C(i(t.$t("nav.technologies")),1)]),_:1})]),e("li",null,[p(y(k),{to:"/about",class:"footer__link"},{default:$(()=>[C(i(t.$t("nav.about")),1)]),_:1})]),e("li",null,[p(y(k),{to:"/contact",class:"footer__link"},{default:$(()=>[C(i(t.$t("nav.contact")),1)]),_:1})])])]),e("div",ke,[e("h4",$e,i(t.$t("footer.solutions")),1),o[4]||(o[4]=N('<ul class="footer__list" data-v-2f291a3e><li data-v-2f291a3e><a href="#" class="footer__link" data-v-2f291a3e>IoT Development</a></li><li data-v-2f291a3e><a href="#" class="footer__link" data-v-2f291a3e>Smart Home Solutions</a></li><li data-v-2f291a3e><a href="#" class="footer__link" data-v-2f291a3e>Hardware Design</a></li><li data-v-2f291a3e><a href="#" class="footer__link" data-v-2f291a3e>Cloud Integration</a></li><li data-v-2f291a3e><a href="#" class="footer__link" data-v-2f291a3e>Consulting</a></li></ul>',1))]),e("div",we,[e("h4",ze,i(t.$t("nav.contact")),1),e("div",Ce,[o[5]||(o[5]=e("span",{class:"contact-label"},"Email:",-1)),e("a",Te,i(t.$t("contact.info.email")),1)]),e("div",Ie,[o[6]||(o[6]=e("span",{class:"contact-label"},"Phone:",-1)),e("a",Pe,i(t.$t("contact.info.phone")),1)]),e("div",Se,[o[7]||(o[7]=e("span",{class:"contact-label"},"Address:",-1)),e("span",Ae,i(t.$t("contact.info.address")),1)])])]),e("div",Me,[e("div",je,[e("p",null,i(t.$t("footer.copyright")),1)]),e("div",Ee,[e("div",Le,[e("button",{class:_(["language-btn",{active:t.$i18n.locale==="cs"}]),onClick:o[0]||(o[0]=s=>a("cs"))}," CS ",2),e("button",{class:_(["language-btn",{active:t.$i18n.locale==="en"}]),onClick:o[1]||(o[1]=s=>a("en"))}," EN ",2)])]),e("div",Fe,[e("a",Re,i(t.$t("footer.privacy")),1),e("a",De,i(t.$t("footer.terms")),1)])])])]))}}),Ve=P(xe,[["__scopeId","data-v-2f291a3e"]]),Ke={class:"particle-background"},Oe=80,B=150,O=100,Ne=I({__name:"ParticleBackground",setup(m){const d=M(null);let a=null,t=[];const o=["#00FFFF","#00FF7F","#FF00FF"];let s={x:0,y:0},r,c;const f=l=>({x:Math.random()*r.width,y:Math.random()*r.height,vx:(Math.random()-.5)*.5,vy:(Math.random()-.5)*.5,size:Math.random()*2+1,opacity:Math.random()*.5+.3,color:o[Math.floor(Math.random()*o.length)],connections:[]}),n=()=>{t=[];for(let l=0;l<Oe;l++)t.push(f())},v=l=>{l.x+=l.vx,l.y+=l.vy;const w=s.x-l.x,A=s.y-l.y,T=Math.sqrt(w*w+A*A);if(T<O){const L=(O-T)/O;l.vx+=w/T*L*.01,l.vy+=A/T*L*.01}(l.x<0||l.x>r.width)&&(l.vx*=-1,l.x=Math.max(0,Math.min(r.width,l.x))),(l.y<0||l.y>r.height)&&(l.vy*=-1,l.y=Math.max(0,Math.min(r.height,l.y))),l.vx*=.99,l.vy*=.99,l.opacity+=(Math.random()-.5)*.01,l.opacity=Math.max(.1,Math.min(.8,l.opacity))},h=l=>{c.save(),c.globalAlpha=l.opacity,c.fillStyle=l.color,c.shadowBlur=10,c.shadowColor=l.color,c.beginPath(),c.arc(l.x,l.y,l.size,0,Math.PI*2),c.fill(),c.restore()},u=()=>{for(let l=0;l<t.length;l++)for(let w=l+1;w<t.length;w++){const A=t[l].x-t[w].x,T=t[l].y-t[w].y,L=Math.sqrt(A*A+T*T);if(L<B){const U=(1-L/B)*.3;c.save(),c.globalAlpha=U,c.strokeStyle=t[l].color,c.lineWidth=.5,c.shadowBlur=5,c.shadowColor=t[l].color,c.beginPath(),c.moveTo(t[l].x,t[l].y),c.lineTo(t[w].x,t[w].y),c.stroke(),c.restore()}}},z=()=>{c.clearRect(0,0,r.width,r.height),t.forEach(l=>{v(l),h(l)}),u(),a=requestAnimationFrame(z)},S=()=>{r&&(r.width=window.innerWidth,r.height=window.innerHeight,n())},j=l=>{s.x=l.clientX,s.y=l.clientY},E=()=>{S()};return F(()=>{r=d.value,c=r.getContext("2d"),S(),n(),z(),window.addEventListener("resize",E),window.addEventListener("mousemove",j)}),V(()=>{a&&cancelAnimationFrame(a),window.removeEventListener("resize",E),window.removeEventListener("mousemove",j)}),(l,w)=>(b(),g("div",Ke,[e("canvas",{ref_key:"canvasRef",ref:d,class:"particle-canvas"},null,512)]))}}),Be=P(Ne,[["__scopeId","data-v-0bdabcf4"]]),He={id:"app",class:"app"},We={class:"main-content"},Ze=I({__name:"App",setup(m){return(d,a)=>(b(),g("div",He,[p(Be),p(ve),e("main",We,[p(y(q))]),p(Ve)]))}}),Ue=P(Ze,[["__scopeId","data-v-e8c19943"]]),qe="modulepreload",Ge=function(m){return"/"+m},H={},R=function(d,a,t){let o=Promise.resolve();if(a&&a.length>0){let r=function(n){return Promise.all(n.map(v=>Promise.resolve(v).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const c=document.querySelector("meta[property=csp-nonce]"),f=(c==null?void 0:c.nonce)||(c==null?void 0:c.getAttribute("nonce"));o=r(a.map(n=>{if(n=Ge(n),n in H)return;H[n]=!0;const v=n.endsWith(".css"),h=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${n}"]${h}`))return;const u=document.createElement("link");if(u.rel=v?"stylesheet":qe,v||(u.as="script"),u.crossOrigin="",u.href=n,f&&u.setAttribute("nonce",f),document.head.appendChild(u),v)return new Promise((z,S)=>{u.addEventListener("load",z),u.addEventListener("error",()=>S(new Error(`Unable to preload CSS for ${n}`)))})}))}function s(r){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=r,window.dispatchEvent(c),!c.defaultPrevented)throw r}return o.then(r=>{for(const c of r||[])c.status==="rejected"&&s(c.reason);return d().catch(s)})},Ye={class:"hero"},Je={class:"hero__background"},Qe={class:"container hero__container"},Xe={class:"hero__content"},et={class:"badge-text"},tt=["innerHTML"],ot={class:"stat"},st={class:"stat-label"},nt={class:"stat"},at={class:"stat-label"},it={class:"stat"},lt={class:"stat-label"},ct={class:"visual-container"},rt={class:"floating-elements"},dt={class:"central-hub"},ut={class:"scroll-text"},vt=I({__name:"HeroSection",setup(m){const d=M(null),a=M(!1);let t=null,o,s;const r=()=>{console.log("Playing demo...")},c=()=>{if(!d.value)return;o=d.value,s=o.getContext("2d");const f=()=>{o.width=o.offsetWidth,o.height=o.offsetHeight};f(),window.addEventListener("resize",f);const n=[],v=()=>({x:-20,y:Math.random()*o.height,vx:1+Math.random()*2,vy:(Math.random()-.5)*.5,opacity:.3+Math.random()*.4});for(let u=0;u<5;u++)n.push(v());const h=()=>{s.clearRect(0,0,o.width,o.height),n.forEach((u,z)=>{if(u.x+=u.vx,u.y+=u.vy,u.x>o.width+20){n[z]=v();return}s.save(),s.globalAlpha=u.opacity,s.fillStyle="#00FFFF",s.shadowBlur=10,s.shadowColor="#00FFFF",s.beginPath(),s.arc(u.x,u.y,2,0,Math.PI*2),s.fill(),s.strokeStyle="#00FFFF",s.lineWidth=1,s.beginPath(),s.moveTo(u.x-20,u.y),s.lineTo(u.x,u.y),s.stroke(),s.restore()}),t=requestAnimationFrame(h)};h()};return F(()=>{setTimeout(()=>{a.value=!0},100),setTimeout(()=>{c()},500)}),V(()=>{t&&cancelAnimationFrame(t)}),(f,n)=>(b(),g("section",Ye,[e("div",Je,[e("canvas",{ref_key:"heroCanvasRef",ref:d,class:"hero__canvas"},null,512)]),e("div",Qe,[e("div",Xe,[e("div",{class:_(["hero__badge",{"animate-fade-in":a.value}])},[e("span",et,i(f.$t("hero.badge")),1)],2),e("h1",{class:_(["hero__title",{"animate-slide-in-up delay-200":a.value}])},[e("span",{innerHTML:f.$t("hero.title",{highlight:`<span class='title-highlight'>${f.$t("hero.titleHighlight")}</span>`})},null,8,tt)],2),e("p",{class:_(["hero__subtitle",{"animate-slide-in-up delay-300":a.value}])},i(f.$t("hero.subtitle")),3),e("div",{class:_(["hero__actions",{"animate-slide-in-up delay-500":a.value}])},[p(y(k),{to:"/contact",class:"btn btn-primary hero__cta"},{default:$(()=>[e("span",null,i(f.$t("hero.exploreSolutions")),1),n[0]||(n[0]=e("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1))]),_:1,__:[0]}),e("button",{class:"btn btn-ghost hero__demo",onClick:r},[n[1]||(n[1]=e("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[e("polygon",{points:"5,3 19,12 5,21",fill:"currentColor"})],-1)),e("span",null,i(f.$t("hero.watchDemo")),1)])],2),e("div",{class:_(["hero__stats",{"animate-fade-in delay-700":a.value}])},[e("div",ot,[n[2]||(n[2]=e("div",{class:"stat-number"},"500+",-1)),e("div",st,i(f.$t("hero.stats.projects")),1)]),e("div",nt,[n[3]||(n[3]=e("div",{class:"stat-number"},"50M+",-1)),e("div",at,i(f.$t("hero.stats.devices")),1)]),e("div",it,[n[4]||(n[4]=e("div",{class:"stat-number"},"99.9%",-1)),e("div",lt,i(f.$t("hero.stats.uptime")),1)])],2)]),e("div",{class:_(["hero__visual",{"animate-scale-in delay-400":a.value}])},[e("div",ct,[e("div",rt,[e("div",{class:_(["element element--1",{"animate-float":a.value}])},null,2),e("div",{class:_(["element element--2",{"animate-float delay-100":a.value}])},null,2),e("div",{class:_(["element element--3",{"animate-float delay-200":a.value}])},null,2)]),e("div",dt,[e("div",{class:_(["hub-core",{"animate-pulse-neon":a.value}])},null,2),n[5]||(n[5]=e("div",{class:"hub-rings"},[e("div",{class:"ring ring--1"}),e("div",{class:"ring ring--2"}),e("div",{class:"ring ring--3"})],-1))])])],2)]),e("div",{class:_(["hero__scroll-indicator",{"animate-fade-in delay-1000":a.value}])},[e("div",ut,i(f.$t("hero.scrollToExplore")),1),n[6]||(n[6]=e("div",{class:"scroll-arrow"},null,-1))],2)]))}}),ht=P(vt,[["__scopeId","data-v-c3de6142"]]),pt={class:"features section"},mt={class:"container"},ft={class:"features__header"},_t={class:"features__title"},gt={class:"features__subtitle"},yt={class:"features__grid"},bt={class:"feature-card__icon"},kt={class:"icon-placeholder"},$t={class:"feature-card__title"},wt={class:"feature-card__description"},zt={class:"feature-card__stats"},Ct={class:"stat"},Tt={class:"stat-value"},It={class:"stat-label"},Pt=I({__name:"FeaturesSection",setup(m){const d=M(!1),a=[{id:1,titleKey:"features.smartConnectivity.title",descriptionKey:"features.smartConnectivity.description",iconText:"⚡",stat:{value:"<1ms",labelKey:"stats.latency"}},{id:2,titleKey:"features.realTimeAnalytics.title",descriptionKey:"features.realTimeAnalytics.description",iconText:"🧠",stat:{value:"95%",labelKey:"stats.accuracy"}},{id:3,titleKey:"features.secureInfrastructure.title",descriptionKey:"features.secureInfrastructure.description",iconText:"🛡️",stat:{value:"256-bit",labelKey:"stats.encryption"}},{id:4,titleKey:"features.scalablePlatform.title",descriptionKey:"features.scalablePlatform.description",iconText:"📈",stat:{value:"10M+",labelKey:"stats.devices"}}];return F(()=>{const t=new IntersectionObserver(s=>{s.forEach(r=>{r.isIntersecting&&(d.value=!0)})},{threshold:.1}),o=document.querySelector(".features");o&&t.observe(o)}),(t,o)=>(b(),g("section",pt,[e("div",mt,[e("div",ft,[e("h2",_t,i(t.$t("features.title")),1),e("p",gt,i(t.$t("features.subtitle")),1)]),e("div",yt,[(b(),g(D,null,x(a,(s,r)=>e("div",{key:s.id,class:_(["feature-card",{"animate-scale-in":d.value}]),style:Z({animationDelay:`${r*.1}s`})},[e("div",bt,[e("div",kt,i(s.iconText),1)]),e("h3",$t,i(t.$t(s.titleKey)),1),e("p",wt,i(t.$t(s.descriptionKey)),1),e("div",zt,[e("div",Ct,[e("span",Tt,i(s.stat.value),1),e("span",It,i(t.$t(s.stat.labelKey)),1)])])],6)),64))])])]))}}),St=P(Pt,[["__scopeId","data-v-ad621711"]]),At={class:"technologies-preview section"},Mt={class:"container"},jt={class:"technologies__header"},Et={class:"technologies__title"},Lt={class:"technologies__subtitle"},Ft={class:"technologies__content"},Rt={class:"tech-showcase"},Dt={class:"tech-visual"},xt={class:"tech-nodes"},Vt=["onClick"],Kt={class:"node-icon"},Ot={class:"node-label"},Nt={class:"tech-details"},Bt={key:0,class:"tech-card"},Ht={class:"tech-card__title"},Wt={class:"tech-card__description"},Zt={class:"tech-specs"},Ut={class:"spec-label"},qt={class:"spec-value"},Gt={class:"tech-features"},Yt={class:"features-title"},Jt={class:"features-list"},Qt={class:"tech-stats"},Xt={class:"stat-card"},eo={class:"stat-label"},to={class:"stat-card"},oo={class:"stat-label"},so={class:"stat-card"},no={class:"stat-label"},ao=I({__name:"TechnologiesPreview",setup(m){const d=M(null),a=M(0),t=[{id:1,x:20,y:30,icon:"🔗",label:"technologies.connectivity.name"},{id:2,x:50,y:20,icon:"🧠",label:"technologies.aiAnalytics.name"},{id:3,x:80,y:35,icon:"☁️",label:"technologies.cloud.title"},{id:4,x:30,y:70,icon:"🔒",label:"technologies.security.name"},{id:5,x:70,y:75,icon:"📊",label:"technologies.ai.title"}],o=[{titleKey:"technologies.iot.title",descriptionKey:"technologies.iot.description",specs:[{label:"Protocols",value:"15+"},{label:"Range",value:"10km+"},{label:"Latency",value:"<1ms"}],features:["technologies.iot.features.autoDiscovery","technologies.iot.features.meshNetworking","technologies.iot.features.adaptiveProtocol","technologies.iot.features.edgeToCloud"]},{titleKey:"technologies.ai.title",descriptionKey:"technologies.ai.description",specs:[{label:"Models",value:"50+"},{label:"Accuracy",value:"99.5%"},{label:"Processing",value:"Real-time"}],features:["technologies.ai.features.predictiveAnalytics","technologies.ai.features.anomalyDetection","technologies.ai.features.nlp","technologies.ai.features.computerVision"]},{titleKey:"technologies.cloud.title",descriptionKey:"technologies.cloud.description",specs:[{label:"Uptime",value:"99.99%"},{label:"Regions",value:"25+"},{label:"Scale",value:"Unlimited"}],features:["technologies.cloud.features.autoScaling","technologies.cloud.features.globalEdge","technologies.cloud.features.multiCloud","technologies.cloud.features.disasterRecovery"]},{titleKey:"technologies.security.title",descriptionKey:"technologies.security.description",specs:[{label:"Encryption",value:"AES-256"},{label:"Compliance",value:"SOC2, ISO27001"},{label:"Authentication",value:"Multi-factor"}],features:["technologies.security.features.zeroTrust","technologies.security.features.blockchain","technologies.security.features.threatIntelligence","technologies.security.features.automatedUpdates"]}],s=W(()=>o[a.value]),r=n=>{a.value=n};let c=null;const f=()=>{if(!d.value)return;const n=d.value,v=n.getContext("2d"),h=()=>{n.width=n.offsetWidth,n.height=n.offsetHeight};h(),window.addEventListener("resize",h);const u=()=>{v.clearRect(0,0,n.width,n.height),v.strokeStyle="rgba(0, 255, 255, 0.3)",v.lineWidth=1;for(let z=0;z<t.length;z++)for(let S=z+1;S<t.length;S++){const j=t[z],E=t[S],l=j.x/100*n.width,w=j.y/100*n.height,A=E.x/100*n.width,T=E.y/100*n.height;v.beginPath(),v.moveTo(l,w),v.lineTo(A,T),v.stroke()}c=requestAnimationFrame(u)};u()};return F(()=>{setTimeout(()=>{f()},500);const n=setInterval(()=>{a.value=(a.value+1)%o.length},5e3);V(()=>{clearInterval(n),c&&cancelAnimationFrame(c)})}),(n,v)=>(b(),g("section",At,[e("div",Mt,[e("div",jt,[e("h2",Et,i(n.$t("technologies.title")),1),e("p",Lt,i(n.$t("technologies.subtitle")),1)]),e("div",Ft,[e("div",Rt,[e("div",Dt,[e("canvas",{ref_key:"techCanvasRef",ref:d,class:"tech-canvas"},null,512),e("div",xt,[(b(),g(D,null,x(t,(h,u)=>e("div",{key:h.id,class:_(["tech-node",{active:a.value===u}]),style:Z({left:h.x+"%",top:h.y+"%",animationDelay:`${u*.2}s`}),onClick:z=>r(u)},[e("div",Kt,i(h.icon),1),e("div",Ot,i(n.$t(h.label)),1)],14,Vt)),64))])]),e("div",Nt,[s.value?(b(),g("div",Bt,[e("h3",Ht,i(n.$t(s.value.titleKey)),1),e("p",Wt,i(n.$t(s.value.descriptionKey)),1),e("div",Zt,[(b(!0),g(D,null,x(s.value.specs,h=>(b(),g("div",{key:h.label,class:"spec-item"},[e("span",Ut,i(h.label),1),e("span",qt,i(h.value),1)]))),128))]),e("div",Gt,[e("h4",Yt,i(n.$t("technologies.keyFeatures")),1),e("ul",Jt,[(b(!0),g(D,null,x(s.value.features,h=>(b(),g("li",{key:h},i(n.$t(h)),1))),128))])])])):G("",!0)])]),e("div",Qt,[e("div",Xt,[v[0]||(v[0]=e("div",{class:"stat-number"},"15+",-1)),e("div",eo,i(n.$t("technologies.stats.technologies")),1)]),e("div",to,[v[1]||(v[1]=e("div",{class:"stat-number"},"99.9%",-1)),e("div",oo,i(n.$t("technologies.stats.reliability")),1)]),e("div",so,[v[2]||(v[2]=e("div",{class:"stat-number"},"24/7",-1)),e("div",no,i(n.$t("technologies.stats.support")),1)])])])])]))}}),io=P(ao,[["__scopeId","data-v-fdd2a602"]]),lo={class:"cta-section section"},co={class:"container"},ro={class:"cta-content"},uo={class:"cta-title"},vo={class:"cta-subtitle"},ho={class:"cta-actions"},po={class:"cta-features"},mo={class:"feature-item"},fo={class:"feature-item"},_o={class:"feature-item"},go=I({__name:"CTASection",setup(m){return(d,a)=>(b(),g("section",lo,[e("div",co,[e("div",ro,[e("h2",uo,i(d.$t("cta.title")),1),e("p",vo,i(d.$t("cta.subtitle")),1),e("div",ho,[p(y(k),{to:"/contact",class:"btn btn-primary cta-primary"},{default:$(()=>[e("span",null,i(d.$t("cta.button")),1),a[0]||(a[0]=e("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1))]),_:1,__:[0]}),p(y(k),{to:"/solutions",class:"btn btn-outline cta-secondary"},{default:$(()=>[e("span",null,i(d.$t("cta.contact")),1)]),_:1})]),e("div",po,[e("div",mo,[a[1]||(a[1]=e("span",{class:"feature-icon"},"✓",-1)),e("span",null,i(d.$t("cta.features.consultation")),1)]),e("div",fo,[a[2]||(a[2]=e("span",{class:"feature-icon"},"✓",-1)),e("span",null,i(d.$t("cta.features.trial")),1)]),e("div",_o,[a[3]||(a[3]=e("span",{class:"feature-icon"},"✓",-1)),e("span",null,i(d.$t("cta.features.support")),1)])])])])]))}}),yo=P(go,[["__scopeId","data-v-edf89e9d"]]),bo={class:"home-view"},ko=I({__name:"HomeView",setup(m){return(d,a)=>(b(),g("div",bo,[p(ht),p(St),p(io),p(yo)]))}}),$o=P(ko,[["__scopeId","data-v-4b95c547"]]),wo=Y({history:J("/"),routes:[{path:"/",name:"home",component:$o},{path:"/solutions",name:"solutions",component:()=>R(()=>import("./SolutionsView-MzyRlEEI.js"),__vite__mapDeps([0,1,2,3]))},{path:"/technologies",name:"technologies",component:()=>R(()=>import("./TechnologiesView-B4aufG_d.js"),__vite__mapDeps([4,1,2,5]))},{path:"/about",name:"about",component:()=>R(()=>import("./AboutView-CrXYiasd.js"),__vite__mapDeps([6,1,2,7]))},{path:"/contact",name:"contact",component:()=>R(()=>import("./ContactView-BXSRbHo2.js"),__vite__mapDeps([8,1,2,9]))}],scrollBehavior(m,d,a){return a||{top:0}}}),zo={home:"Home",solutions:"Solutions",technologies:"Technologies",about:"About",contact:"Contact",getStarted:"Get Started",switchLanguage:"Switch Language"},Co={badge:"Next Generation IoT",title:"Welcome to the Future of Connected Intelligence",titleHighlight:"Future",subtitle:"Transform your world with cutting-edge IoT solutions that bridge the gap between imagination and reality. Where every device becomes intelligent, every connection meaningful, and every data point a step toward tomorrow.",exploreSolutions:"Explore Solutions",watchDemo:"Watch Demo",scrollToExplore:"Scroll to explore",stats:{projects:"Projects Delivered",devices:"Devices Connected",uptime:"Uptime Guaranteed"}},To={title:"Revolutionary IoT Solutions",subtitle:"Discover how our advanced technology transforms industries and creates intelligent ecosystems",smartConnectivity:{title:"Smart Connectivity",description:"Seamless device integration with ultra-low latency and 99.9% reliability"},realTimeAnalytics:{title:"Real-time Analytics",description:"Advanced AI-powered insights that turn data into actionable intelligence"},secureInfrastructure:{title:"Secure Infrastructure",description:"Enterprise-grade security with end-to-end encryption and threat protection"},scalablePlatform:{title:"Scalable Platform",description:"Cloud-native architecture that grows with your business needs"}},Io={title:"Cutting-Edge Technologies",subtitle:"Powered by the latest innovations in IoT, AI, and cloud computing",benefits:"Benefits",keyFeatures:"Key Features",technicalSpecs:"Technical Specifications",connectivity:{name:"Connectivity",description:"Advanced multi-protocol connectivity solutions ensuring seamless communication between devices across various networks and environments.",benefits:{multiProtocol:"Multi-protocol support (WiFi 6, 5G, LoRaWAN, Bluetooth 5.0)",extendedRange:"Extended range up to 10km",lowLatency:"Ultra-low latency (<1ms)",failover:"Automatic failover and redundancy"},stats:{protocols:"Protocols",range:"Range",latency:"Latency",uptime:"Uptime"}},aiAnalytics:{name:"AI & Analytics",description:"Intelligent data processing with machine learning algorithms that transform raw sensor data into actionable business insights.",benefits:{anomalyDetection:"Real-time anomaly detection",predictiveMaintenance:"Predictive maintenance capabilities",automatedDecision:"Automated decision making",patternRecognition:"99.5% accuracy in pattern recognition"},stats:{models:"Models",accuracy:"Accuracy",processing:"Processing"}},security:{name:"Security",description:"Enterprise-grade security framework with end-to-end encryption, zero-trust architecture, and compliance with international standards.",benefits:{encryption:"End-to-end AES-256 encryption",zeroTrust:"Zero-trust security model",compliance:"SOC2 and ISO27001 compliance",mfa:"Multi-factor authentication"},stats:{encryption:"Encryption",compliance:"Compliance",uptime:"Uptime"}},iot:{title:"IoT Ecosystem",description:"Complete device management and orchestration platform",features:{autoDiscovery:"Auto-discovery and pairing",meshNetworking:"Mesh networking capabilities",adaptiveProtocol:"Adaptive protocol switching",edgeToCloud:"Edge-to-cloud connectivity"}},ai:{title:"Artificial Intelligence",description:"Machine learning algorithms for predictive analytics",features:{predictiveAnalytics:"Predictive analytics",anomalyDetection:"Anomaly detection",nlp:"Natural language processing",computerVision:"Computer vision integration"}},cloud:{title:"Cloud Infrastructure",description:"Scalable and reliable cloud-native solutions",features:{autoScaling:"Auto-scaling infrastructure",globalEdge:"Global edge deployment",multiCloud:"Multi-cloud support",disasterRecovery:"Disaster recovery"}}},Po={title:"Industry Solutions",subtitle:"Tailored IoT solutions for every industry vertical",keyFeatures:"Key Features",smartCities:{title:"Smart Cities",description:"Transform urban infrastructure with intelligent monitoring, traffic optimization, and energy management systems.",features:["Traffic Flow Optimization","Smart Lighting Systems","Waste Management","Environmental Monitoring"],statLabel:"Energy Savings"},manufacturing:{title:"Industrial Manufacturing",description:"Revolutionize production with predictive maintenance, quality control, and supply chain optimization.",features:["Predictive Maintenance","Quality Control Automation","Energy Optimization","Supply Chain Visibility"],statLabel:"Efficiency Increase"},healthcare:{title:"Healthcare & Medical",description:"Enhance patient care with remote monitoring, asset tracking, and intelligent medical devices.",features:["Patient Monitoring","Asset Tracking","Environmental Control","Emergency Response"],statLabel:"Response Time Improvement"},agriculture:{title:"Smart Agriculture",description:"Optimize crop yields with precision farming, soil monitoring, and automated irrigation systems.",features:["Soil Monitoring","Automated Irrigation","Crop Health Tracking","Weather Integration"],statLabel:"Yield Increase"}},So={title:"About IoTVision",subtitle:"Leading the future of connected intelligence",mission:{title:"Our Mission",description:"To democratize IoT technology and make intelligent connectivity accessible to businesses of all sizes, driving innovation and efficiency across industries."},vision:{title:"Our Vision",description:"A world where every device is intelligent, every connection is meaningful, and every data point contributes to a smarter, more sustainable future."},values:{title:"Our Values",innovation:{title:"Innovation",description:"Pushing the boundaries of what's possible with cutting-edge technology"},reliability:{title:"Reliability",description:"Building robust solutions that businesses can depend on"},security:{title:"Security",description:"Protecting data and privacy with enterprise-grade security"},sustainability:{title:"Sustainability",description:"Creating solutions that contribute to a more sustainable future"}},team:{title:"Expert Team",description:"Our team of IoT specialists, data scientists, and engineers brings decades of combined experience in creating innovative connected solutions."}},Ao={title:"Get in Touch",subtitle:"Ready to transform your business with IoT? Let's discuss your project.",form:{name:"Full Name",email:"Email Address",company:"Company Name",phone:"Phone Number",subject:"Subject",message:"Message",send:"Send Message",sending:"Sending...",success:"Message sent successfully!",error:"Failed to send message. Please try again."},info:{address:"Prague, Czech Republic",email:"info[at]iotvision.com",phone:"+*********** 789",hours:"Mon-Fri: 9:00 - 18:00"}},Mo={title:"Ready to Start Your IoT Journey?",subtitle:"Join thousands of businesses already transforming with our IoT solutions",button:"Get Started Today",contact:"Contact Us",features:{consultation:"Free Consultation",trial:"30-Day Trial",support:"24/7 Support"}},jo={learnMore:"Learn More"},Eo={latency:"Latency",accuracy:"Accuracy",encryption:"Encryption",devices:"Devices"},Lo={description:"Leading provider of innovative IoT solutions for the connected world.",quickLinks:"Quick Links",solutions:"Solutions",company:"Company",legal:"Legal",privacy:"Privacy Policy",terms:"Terms of Service",cookies:"Cookie Policy",copyright:"© 2024 IoTVision. All rights reserved.",madeWith:"Made with love using Vue.js 3 and modern web technologies."},Fo={nav:zo,hero:Co,features:To,technologies:Io,solutions:Po,about:So,contact:Ao,cta:Mo,common:jo,stats:Eo,footer:Lo},Ro={home:"Domů",solutions:"Řešení",technologies:"Technologie",about:"O nás",contact:"Kontakt",getStarted:"Začít",switchLanguage:"Přepnout jazyk"},Do={badge:"IoT Nové Generace",title:"Vítejte v budoucnosti propojené inteligence",titleHighlight:"budoucnosti",subtitle:"Transformujte svět pomocí špičkových IoT řešení, která překlenují propast mezi představivostí a realitou. Kde se každé zařízení stává inteligentním, každé spojení smysluplným a každý datový bod krokem k zítřku.",exploreSolutions:"Prozkoumat řešení",watchDemo:"Sledovat demo",scrollToExplore:"Rolujte pro prozkoumání",stats:{projects:"Dokončených projektů",devices:"Připojených zařízení",uptime:"Garantovaná dostupnost"}},xo={title:"Revoluční IoT řešení",subtitle:"Objevte, jak naše pokročilá technologie transformuje odvětví a vytváří inteligentní ekosystémy",smartConnectivity:{title:"Chytré propojení",description:"Bezproblémová integrace zařízení s ultra-nízkou latencí a 99,9% spolehlivostí"},realTimeAnalytics:{title:"Analýzy v reálném čase",description:"Pokročilé poznatky poháněné AI, které mění data na užitečnou inteligenci"},secureInfrastructure:{title:"Bezpečná infrastruktura",description:"Podniková bezpečnost s end-to-end šifrováním a ochranou před hrozbami"},scalablePlatform:{title:"Škálovatelná platforma",description:"Cloud-native architektura, která roste s potřebami vašeho podnikání"}},Vo={title:"Špičkové technologie",subtitle:"Poháněno nejnovějšími inovacemi v IoT, AI a cloud computingu",benefits:"Výhody",keyFeatures:"Klíčové funkce",technicalSpecs:"Technické specifikace",connectivity:{name:"Konektivita",description:"Pokročilá multi-protokolová řešení konektivity zajišťující bezproblémovou komunikaci mezi zařízeními napříč různými sítěmi a prostředími.",benefits:{multiProtocol:"Podpora více protokolů (WiFi 6, 5G, LoRaWAN, Bluetooth 5.0)",extendedRange:"Rozšířený dosah až 10 km",lowLatency:"Ultra nízká latence (<1ms)",failover:"Automatické záložní řešení a redundance"},stats:{protocols:"Protokoly",range:"Dosah",latency:"Latence",uptime:"Dostupnost"}},aiAnalytics:{name:"AI a Analytika",description:"Inteligentní zpracování dat s algoritmy strojového učení, které transformují surová senzorová data na užitečné obchodní poznatky.",benefits:{anomalyDetection:"Detekce anomálií v reálném čase",predictiveMaintenance:"Možnosti prediktivní údržby",automatedDecision:"Automatizované rozhodování",patternRecognition:"99,5% přesnost v rozpoznávání vzorů"},stats:{models:"Modely",accuracy:"Přesnost",processing:"Zpracování"}},security:{name:"Bezpečnost",description:"Podnikový bezpečnostní framework s end-to-end šifrováním, zero-trust architekturou a soulad s mezinárodními standardy.",benefits:{encryption:"End-to-end AES-256 šifrování",zeroTrust:"Zero-trust bezpečnostní model",compliance:"SOC2 a ISO27001 soulad",mfa:"Vícefaktorová autentizace"},stats:{encryption:"Šifrování",compliance:"Soulad",uptime:"Dostupnost"}},iot:{title:"IoT ekosystém",description:"Kompletní platforma pro správu a orchestraci zařízení",features:{autoDiscovery:"Automatické objevování a párování",meshNetworking:"Možnosti mesh sítí",adaptiveProtocol:"Adaptivní přepínání protokolů",edgeToCloud:"Konektivita edge-to-cloud"}},ai:{title:"Umělá inteligence",description:"Algoritmy strojového učení pro prediktivní analýzy",features:{predictiveAnalytics:"Prediktivní analytika",anomalyDetection:"Detekce anomálií",nlp:"Zpracování přirozeného jazyka",computerVision:"Integrace počítačového vidění"}},cloud:{title:"Cloudová infrastruktura",description:"Škálovatelná a spolehlivá cloud-native řešení",features:{autoScaling:"Automaticky škálující infrastruktura",globalEdge:"Globální edge nasazení",multiCloud:"Podpora více cloudů",disasterRecovery:"Obnova po havárii"}}},Ko={title:"Odvětvová řešení",subtitle:"Přizpůsobená IoT řešení pro každé odvětví",keyFeatures:"Klíčové funkce",smartCities:{title:"Chytrá města",description:"Transformujte městskou infrastrukturu pomocí inteligentního monitoringu, optimalizace dopravy a systémů řízení energie.",features:["Optimalizace dopravního toku","Chytré osvětlovací systémy","Správa odpadu","Monitorování životního prostředí"],statLabel:"Úspora energie"},manufacturing:{title:"Průmyslová výroba",description:"Revolucionalizujte výrobu pomocí prediktivní údržby, kontroly kvality a optimalizace dodavatelského řetězce.",features:["Prediktivní údržba","Automatizace kontroly kvality","Optimalizace energie","Viditelnost dodavatelského řetězce"],statLabel:"Zvýšení efektivity"},healthcare:{title:"Zdravotnictví a medicína",description:"Vylepšete péči o pacienty pomocí vzdáleného monitoringu, sledování majetku a inteligentních zdravotnických zařízení.",features:["Monitorování pacientů","Sledování majetku","Kontrola prostředí","Nouzová reakce"],statLabel:"Zlepšení reakčního času"},agriculture:{title:"Chytré zemědělství",description:"Optimalizujte výnosy plodin pomocí precizního zemědělství, monitoringu půdy a automatizovaných zavlažovacích systémů.",features:["Monitorování půdy","Automatizované zavlažování","Sledování zdraví plodin","Integrace počasí"],statLabel:"Zvýšení výnosu"}},Oo={title:"O společnosti IoTVision",subtitle:"Vedeme budoucnost propojené inteligence",mission:{title:"Naše mise",description:"Demokratizovat IoT technologie a zpřístupnit inteligentní konektivitu podnikům všech velikostí, podporovat inovace a efektivitu napříč odvětvími."},vision:{title:"Naše vize",description:"Svět, kde je každé zařízení inteligentní, každé spojení smysluplné a každý datový bod přispívá k chytřejší, udržitelnější budoucnosti."},values:{title:"Naše hodnoty",innovation:{title:"Inovace",description:"Posouvání hranic možného pomocí špičkových technologií"},reliability:{title:"Spolehlivost",description:"Budování robustních řešení, na která se podniky mohou spolehnout"},security:{title:"Bezpečnost",description:"Ochrana dat a soukromí pomocí podnikové bezpečnosti"},sustainability:{title:"Udržitelnost",description:"Vytváření řešení, která přispívají k udržitelnější budoucnosti"}},team:{title:"Expertní tým",description:"Náš tým IoT specialistů, datových vědců a inženýrů přináší desetiletí kombinovaných zkušeností ve vytváření inovativních propojených řešení."}},No={title:"Kontaktujte nás",subtitle:"Připraveni transformovat své podnikání pomocí IoT? Pojďme diskutovat o vašem projektu.",form:{name:"Celé jméno",email:"E-mailová adresa",company:"Název společnosti",phone:"Telefonní číslo",subject:"Předmět",message:"Zpráva",send:"Odeslat zprávu",sending:"Odesílání...",success:"Zpráva byla úspěšně odeslána!",error:"Nepodařilo se odeslat zprávu. Zkuste to prosím znovu."},info:{address:"Praha, Česká republika",email:"info[at]iotvision.com",phone:"+*********** 789",hours:"Po-Pá: 9:00 - 18:00"}},Bo={title:"Připraveni začít svou IoT cestu?",subtitle:"Připojte se k tisícům podniků, které se již transformují pomocí našich IoT řešení",button:"Začít ještě dnes",contact:"Kontaktujte nás",features:{consultation:"Bezplatná konzultace",trial:"30denní zkušební verze",support:"Podpora 24/7"}},Ho={learnMore:"Zjistit více"},Wo={latency:"Latence",accuracy:"Přesnost",encryption:"Šifrování",devices:"Zařízení"},Zo={description:"Přední poskytovatel inovativních IoT řešení pro propojený svět.",quickLinks:"Rychlé odkazy",solutions:"Řešení",company:"Společnost",legal:"Právní",privacy:"Zásady ochrany osobních údajů",terms:"Podmínky služby",cookies:"Zásady cookies",copyright:"© 2024 IoTVision. Všechna práva vyhrazena.",madeWith:"Vytvořeno s láskou pomocí Vue.js 3 a moderních webových technologií."},Uo={nav:Ro,hero:Do,features:xo,technologies:Vo,solutions:Ko,about:Oo,contact:No,cta:Bo,common:Ho,stats:Wo,footer:Zo},qo=()=>{const m=localStorage.getItem("preferred-language");return m&&["en","cs"].includes(m)?m:(navigator.language||navigator.userLanguage).toLowerCase().startsWith("en")?"en":"cs"},Go=te({legacy:!1,locale:qo(),fallbackLocale:"cs",globalInjection:!0,messages:{en:Fo,cs:Uo}}),K=Q(Ue);K.use(X());K.use(wo);K.use(Go);K.mount("#app");export{P as _};
