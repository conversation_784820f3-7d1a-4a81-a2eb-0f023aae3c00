import{d as f,r as u,G as _,k as d,l as t,t as e,H as b,A as v,I as l,J as r,K as g,v as y,n as $,p as m}from"./vendor-Cao0-QMi.js";import{_ as k}from"./index-M82p7UVk.js";import"./i18n-laVgjn-G.js";const w={class:"contact-view"},V={class:"contact-hero section"},j={class:"container"},M={class:"hero-content"},T={class:"hero-title"},q={class:"hero-subtitle"},C={class:"contact-content section"},S={class:"container"},U={class:"contact-grid"},P={class:"contact-form-section"},B={class:"form-group"},E={for:"name"},I=["placeholder"],D={class:"form-group"},N={for:"email"},z=["placeholder"],A={class:"form-group"},G={for:"company"},F=["placeholder"],H={class:"form-group"},J={for:"phone"},K=["placeholder"],L={class:"form-group"},O={for:"subject"},R={value:""},W={class:"form-group"},Q={for:"message"},X=["placeholder"],Y=["disabled"],Z={key:0},x={key:1},tt={key:2,class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},ot={class:"contact-info-section"},st={class:"contact-methods"},et={class:"contact-method"},nt={class:"method-content"},at={class:"contact-method"},it={class:"method-content"},lt={class:"contact-method"},ct={class:"method-content"},dt={class:"response-time"},rt={class:"response-stats"},mt={class:"response-stat"},pt={class:"stat-label"},ut={class:"response-stat"},vt={class:"stat-label"},ht=f({__name:"ContactView",setup(ft){const c=u(!1),n=_({name:"",email:"",company:"",phone:"",subject:"",message:""}),i=u(""),p=u(""),h=async()=>{c.value=!0,i.value="";try{await new Promise(s=>setTimeout(s,2e3)),Object.keys(n).forEach(s=>{n[s]=""}),i.value="Message sent successfully! We'll get back to you soon.",p.value="success",setTimeout(()=>{i.value=""},5e3)}catch{i.value="Error sending message. Please try again.",p.value="error"}finally{c.value=!1}};return(s,o)=>(m(),d("div",w,[t("section",V,[t("div",j,[t("div",M,[t("h1",T,e(s.$t("contact.title")),1),t("p",q,e(s.$t("contact.subtitle")),1)])])]),t("section",C,[t("div",S,[t("div",U,[t("div",P,[t("h2",null,e(s.$t("contact.form.send")),1),t("form",{onSubmit:b(h,["prevent"]),class:"contact-form"},[t("div",B,[t("label",E,e(s.$t("contact.form.name"))+" *",1),l(t("input",{type:"text",id:"name","onUpdate:modelValue":o[0]||(o[0]=a=>n.name=a),required:"",class:"form-input",placeholder:s.$t("contact.form.name")},null,8,I),[[r,n.name]])]),t("div",D,[t("label",N,e(s.$t("contact.form.email"))+" *",1),l(t("input",{type:"email",id:"email","onUpdate:modelValue":o[1]||(o[1]=a=>n.email=a),required:"",class:"form-input",placeholder:s.$t("contact.form.email")},null,8,z),[[r,n.email]])]),t("div",A,[t("label",G,e(s.$t("contact.form.company")),1),l(t("input",{type:"text",id:"company","onUpdate:modelValue":o[2]||(o[2]=a=>n.company=a),class:"form-input",placeholder:s.$t("contact.form.company")},null,8,F),[[r,n.company]])]),t("div",H,[t("label",J,e(s.$t("contact.form.phone")),1),l(t("input",{type:"tel",id:"phone","onUpdate:modelValue":o[3]||(o[3]=a=>n.phone=a),class:"form-input",placeholder:s.$t("contact.form.phone")},null,8,K),[[r,n.phone]])]),t("div",L,[t("label",O,e(s.$t("contact.form.subject"))+" *",1),l(t("select",{id:"subject","onUpdate:modelValue":o[4]||(o[4]=a=>n.subject=a),required:"",class:"form-select"},[t("option",R,e(s.$t("contact.form.subject")),1),o[6]||(o[6]=y('<option value="general" data-v-4ccb0bd9>General Inquiry</option><option value="demo" data-v-4ccb0bd9>Request Demo</option><option value="partnership" data-v-4ccb0bd9>Partnership</option><option value="support" data-v-4ccb0bd9>Technical Support</option><option value="pricing" data-v-4ccb0bd9>Pricing Information</option>',5))],512),[[g,n.subject]])]),t("div",W,[t("label",Q,e(s.$t("contact.form.message"))+" *",1),l(t("textarea",{id:"message","onUpdate:modelValue":o[5]||(o[5]=a=>n.message=a),required:"",class:"form-textarea",rows:"5",placeholder:s.$t("contact.form.message")},null,8,X),[[r,n.message]])]),t("button",{type:"submit",class:"btn btn-primary form-submit",disabled:c.value},[c.value?(m(),d("span",x,e(s.$t("contact.form.sending")),1)):(m(),d("span",Z,e(s.$t("contact.form.send")),1)),c.value?v("",!0):(m(),d("svg",tt,o[7]||(o[7]=[t("path",{d:"M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,Y),i.value?(m(),d("div",{key:0,class:$(["submit-message",p.value])},e(i.value),3)):v("",!0)],32)]),t("div",ot,[t("h2",null,e(s.$t("nav.contact")),1),t("div",st,[t("div",et,[o[10]||(o[10]=t("div",{class:"method-icon"},"📧",-1)),t("div",nt,[o[8]||(o[8]=t("h3",null,"Email",-1)),t("p",null,e(s.$t("contact.info.email")),1),o[9]||(o[9]=t("p",null,"<EMAIL>",-1))])]),t("div",at,[o[12]||(o[12]=t("div",{class:"method-icon"},"📞",-1)),t("div",it,[o[11]||(o[11]=t("h3",null,"Phone",-1)),t("p",null,e(s.$t("contact.info.phone")),1),t("p",null,e(s.$t("contact.info.hours")),1)])]),t("div",lt,[o[14]||(o[14]=t("div",{class:"method-icon"},"📍",-1)),t("div",ct,[o[13]||(o[13]=t("h3",null,"Address",-1)),t("p",null,e(s.$t("contact.info.address")),1)])])]),t("div",dt,[t("h3",null,e(s.$t("contact.responseTime.title")),1),t("div",rt,[t("div",mt,[o[15]||(o[15]=t("span",{class:"stat-value"},"< 2h",-1)),t("span",pt,e(s.$t("contact.responseTime.average")),1)]),t("div",ut,[o[16]||(o[16]=t("span",{class:"stat-value"},"24/7",-1)),t("span",vt,e(s.$t("contact.responseTime.emergency")),1)])])])])])])])]))}}),yt=k(ht,[["__scopeId","data-v-4ccb0bd9"]]);export{yt as default};
