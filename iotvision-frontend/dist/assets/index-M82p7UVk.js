const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/SolutionsView-BDE0avr4.js","assets/vendor-Cao0-QMi.js","assets/i18n-laVgjn-G.js","assets/SolutionsView-DqDdkGfm.css","assets/TechnologiesView-DwX5tSlO.js","assets/TechnologiesView-B7E63xxa.css","assets/AboutView-C5_t3nTL.js","assets/AboutView-T51yqGEq.css","assets/ContactView-XsnzHoPy.js","assets/ContactView-DOqHjBVX.css"])))=>i.map(i=>d[i]);
import{d as A,r as M,o as R,b as O,k as y,n as f,l as e,j as p,u as _,R as k,m as $,p as b,q as C,t as o,c as B,v as U,x as q,F as D,y as V,z as Z,A as G,B as J,C as Y,D as Q,E as X}from"./vendor-Cao0-QMi.js";import{u as ee,c as te}from"./i18n-laVgjn-G.js";(function(){const d=document.createElement("link").relList;if(d&&d.supports&&d.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))t(a);new MutationObserver(a=>{for(const n of a)if(n.type==="childList")for(const c of n.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&t(c)}).observe(document,{childList:!0,subtree:!0});function i(a){const n={};return a.integrity&&(n.integrity=a.integrity),a.referrerPolicy&&(n.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?n.credentials="include":a.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function t(a){if(a.ep)return;a.ep=!0;const n=i(a);fetch(a.href,n)}})();const oe={class:"nav container"},ne={class:"nav__logo"},se={class:"nav__item"},ae={class:"nav__item"},ie={class:"nav__item"},le={class:"nav__item"},re={class:"nav__item"},ce={class:"nav__cta"},de=["aria-expanded"],ue=A({__name:"AppHeader",setup(m){const d=M(!1),i=M(!1),t=()=>{d.value=window.scrollY>50},a=()=>{i.value=!i.value},n=()=>{i.value=!1};return R(()=>{window.addEventListener("scroll",t)}),O(()=>{window.removeEventListener("scroll",t)}),(c,r)=>(b(),y("header",{class:f(["header",{"header--scrolled":d.value}])},[e("nav",oe,[e("div",ne,[p(_(k),{to:"/",class:"logo-link"},{default:$(()=>r[0]||(r[0]=[e("span",{class:"logo-text"},"IoT",-1),e("span",{class:"logo-accent"},"Vision",-1)])),_:1,__:[0]})]),e("ul",{class:f(["nav__menu",{"nav__menu--open":i.value}])},[e("li",se,[p(_(k),{to:"/",class:"nav__link",onClick:n},{default:$(()=>[C(o(c.$t("nav.home")),1)]),_:1})]),e("li",ae,[p(_(k),{to:"/solutions",class:"nav__link",onClick:n},{default:$(()=>[C(o(c.$t("nav.solutions")),1)]),_:1})]),e("li",ie,[p(_(k),{to:"/technologies",class:"nav__link",onClick:n},{default:$(()=>[C(o(c.$t("nav.technologies")),1)]),_:1})]),e("li",le,[p(_(k),{to:"/about",class:"nav__link",onClick:n},{default:$(()=>[C(o(c.$t("nav.about")),1)]),_:1})]),e("li",re,[p(_(k),{to:"/contact",class:"nav__link",onClick:n},{default:$(()=>[C(o(c.$t("nav.contact")),1)]),_:1})])],2),e("div",ce,[p(_(k),{to:"/contact",class:"btn btn-primary"},{default:$(()=>[C(o(c.$t("nav.getStarted")),1)]),_:1})]),e("button",{class:"nav__toggle",onClick:a,"aria-expanded":i.value,"aria-label":"Toggle navigation menu"},r[1]||(r[1]=[e("span",{class:"nav__toggle-line"},null,-1),e("span",{class:"nav__toggle-line"},null,-1),e("span",{class:"nav__toggle-line"},null,-1)]),8,de)])],2))}}),I=(m,d)=>{const i=m.__vccOpts||m;for(const[t,a]of d)i[t]=a;return i},ve=I(ue,[["__scopeId","data-v-de3f075c"]]),he={class:"footer"},pe={class:"container"},me={class:"footer__content"},ge={class:"footer__brand"},fe={class:"footer__description"},ye={class:"footer__links"},_e={class:"footer__title"},be={class:"footer__list"},ke={class:"footer__links"},$e={class:"footer__title"},ze={class:"footer__list"},we={href:"#",class:"footer__link"},Ce={href:"#",class:"footer__link"},Te={href:"#",class:"footer__link"},Ae={href:"#",class:"footer__link"},Ie={href:"#",class:"footer__link"},Se={class:"footer__contact"},Pe={class:"footer__title"},Me={class:"contact-item"},je={href:"mailto:<EMAIL>",class:"contact-value"},Le={class:"contact-item"},Ee={href:"tel:+420123456789",class:"contact-value"},Re={class:"contact-item"},Fe={class:"contact-value"},De={class:"footer__bottom"},Ve={class:"footer__copyright"},Oe={class:"footer__language"},xe={class:"language-switcher"},Ke={class:"footer__legal"},Ne={href:"#",class:"footer__link"},He={href:"#",class:"footer__link"},Be=A({__name:"AppFooter",setup(m){B(()=>new Date().getFullYear());const{locale:d}=ee(),i=t=>{d.value=t,localStorage.setItem("preferred-language",t)};return(t,a)=>(b(),y("footer",he,[e("div",pe,[e("div",me,[e("div",ge,[a[2]||(a[2]=e("div",{class:"footer__logo"},[e("span",{class:"logo-text"},"IoT"),e("span",{class:"logo-accent"},"Vision")],-1)),e("p",fe,o(t.$t("footer.description")),1),a[3]||(a[3]=U('<div class="footer__social" data-v-0d9a0bae><a href="#" class="social-link" aria-label="LinkedIn" data-v-0d9a0bae><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-0d9a0bae><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" data-v-0d9a0bae></path></svg></a><a href="#" class="social-link" aria-label="Twitter" data-v-0d9a0bae><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-0d9a0bae><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" data-v-0d9a0bae></path></svg></a><a href="#" class="social-link" aria-label="GitHub" data-v-0d9a0bae><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-0d9a0bae><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" data-v-0d9a0bae></path></svg></a><a href="#" class="social-link" aria-label="Telegram" data-v-0d9a0bae><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-0d9a0bae><path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" data-v-0d9a0bae></path></svg></a></div>',1))]),e("div",ye,[e("h4",_e,o(t.$t("footer.quickLinks")),1),e("ul",be,[e("li",null,[p(_(k),{to:"/",class:"footer__link"},{default:$(()=>[C(o(t.$t("nav.home")),1)]),_:1})]),e("li",null,[p(_(k),{to:"/solutions",class:"footer__link"},{default:$(()=>[C(o(t.$t("nav.solutions")),1)]),_:1})]),e("li",null,[p(_(k),{to:"/technologies",class:"footer__link"},{default:$(()=>[C(o(t.$t("nav.technologies")),1)]),_:1})]),e("li",null,[p(_(k),{to:"/about",class:"footer__link"},{default:$(()=>[C(o(t.$t("nav.about")),1)]),_:1})]),e("li",null,[p(_(k),{to:"/contact",class:"footer__link"},{default:$(()=>[C(o(t.$t("nav.contact")),1)]),_:1})])])]),e("div",ke,[e("h4",$e,o(t.$t("footer.solutions")),1),e("ul",ze,[e("li",null,[e("a",we,o(t.$t("footer.services.iotDevelopment")),1)]),e("li",null,[e("a",Ce,o(t.$t("footer.services.smartHomeSolutions")),1)]),e("li",null,[e("a",Te,o(t.$t("footer.services.hardwareDesign")),1)]),e("li",null,[e("a",Ae,o(t.$t("footer.services.cloudIntegration")),1)]),e("li",null,[e("a",Ie,o(t.$t("footer.services.consulting")),1)])])]),e("div",Se,[e("h4",Pe,o(t.$t("nav.contact")),1),e("div",Me,[a[4]||(a[4]=e("span",{class:"contact-label"},"Email:",-1)),e("a",je,o(t.$t("contact.info.email")),1)]),e("div",Le,[a[5]||(a[5]=e("span",{class:"contact-label"},"Phone:",-1)),e("a",Ee,o(t.$t("contact.info.phone")),1)]),e("div",Re,[a[6]||(a[6]=e("span",{class:"contact-label"},"Address:",-1)),e("span",Fe,o(t.$t("contact.info.address")),1)])])]),e("div",De,[e("div",Ve,[e("p",null,o(t.$t("footer.copyright")),1)]),e("div",Oe,[e("div",xe,[e("button",{class:f(["language-btn",{active:t.$i18n.locale==="cs"}]),onClick:a[0]||(a[0]=n=>i("cs"))}," CS ",2),e("button",{class:f(["language-btn",{active:t.$i18n.locale==="en"}]),onClick:a[1]||(a[1]=n=>i("en"))}," EN ",2)])]),e("div",Ke,[e("a",Ne,o(t.$t("footer.privacy")),1),e("a",He,o(t.$t("footer.terms")),1)])])])]))}}),Ze=I(Be,[["__scopeId","data-v-0d9a0bae"]]),We={class:"particle-background"},Ue=80,N=150,K=100,qe=A({__name:"ParticleBackground",setup(m){const d=M(null);let i=null,t=[];const a=["#00FFFF","#00FF7F","#FF00FF"];let n={x:0,y:0},c,r;const g=l=>({x:Math.random()*c.width,y:Math.random()*c.height,vx:(Math.random()-.5)*.5,vy:(Math.random()-.5)*.5,size:Math.random()*2+1,opacity:Math.random()*.5+.3,color:a[Math.floor(Math.random()*a.length)],connections:[]}),s=()=>{t=[];for(let l=0;l<Ue;l++)t.push(g())},v=l=>{l.x+=l.vx,l.y+=l.vy;const z=n.x-l.x,P=n.y-l.y,T=Math.sqrt(z*z+P*P);if(T<K){const E=(K-T)/K;l.vx+=z/T*E*.01,l.vy+=P/T*E*.01}(l.x<0||l.x>c.width)&&(l.vx*=-1,l.x=Math.max(0,Math.min(c.width,l.x))),(l.y<0||l.y>c.height)&&(l.vy*=-1,l.y=Math.max(0,Math.min(c.height,l.y))),l.vx*=.99,l.vy*=.99,l.opacity+=(Math.random()-.5)*.01,l.opacity=Math.max(.1,Math.min(.8,l.opacity))},h=l=>{r.save(),r.globalAlpha=l.opacity,r.fillStyle=l.color,r.shadowBlur=10,r.shadowColor=l.color,r.beginPath(),r.arc(l.x,l.y,l.size,0,Math.PI*2),r.fill(),r.restore()},u=()=>{for(let l=0;l<t.length;l++)for(let z=l+1;z<t.length;z++){const P=t[l].x-t[z].x,T=t[l].y-t[z].y,E=Math.sqrt(P*P+T*T);if(E<N){const W=(1-E/N)*.3;r.save(),r.globalAlpha=W,r.strokeStyle=t[l].color,r.lineWidth=.5,r.shadowBlur=5,r.shadowColor=t[l].color,r.beginPath(),r.moveTo(t[l].x,t[l].y),r.lineTo(t[z].x,t[z].y),r.stroke(),r.restore()}}},w=()=>{r.clearRect(0,0,c.width,c.height),t.forEach(l=>{v(l),h(l)}),u(),i=requestAnimationFrame(w)},S=()=>{c&&(c.width=window.innerWidth,c.height=window.innerHeight,s())},j=l=>{n.x=l.clientX,n.y=l.clientY},L=()=>{S()};return R(()=>{c=d.value,r=c.getContext("2d"),S(),s(),w(),window.addEventListener("resize",L),window.addEventListener("mousemove",j)}),O(()=>{i&&cancelAnimationFrame(i),window.removeEventListener("resize",L),window.removeEventListener("mousemove",j)}),(l,z)=>(b(),y("div",We,[e("canvas",{ref_key:"canvasRef",ref:d,class:"particle-canvas"},null,512)]))}}),Ge=I(qe,[["__scopeId","data-v-0bdabcf4"]]),Je={id:"app",class:"app"},Ye={class:"main-content"},Qe=A({__name:"App",setup(m){return(d,i)=>(b(),y("div",Je,[p(Ge),p(ve),e("main",Ye,[p(_(q))]),p(Ze)]))}}),Xe=I(Qe,[["__scopeId","data-v-e8c19943"]]),et="modulepreload",tt=function(m){return"/"+m},H={},F=function(d,i,t){let a=Promise.resolve();if(i&&i.length>0){let c=function(s){return Promise.all(s.map(v=>Promise.resolve(v).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const r=document.querySelector("meta[property=csp-nonce]"),g=(r==null?void 0:r.nonce)||(r==null?void 0:r.getAttribute("nonce"));a=c(i.map(s=>{if(s=tt(s),s in H)return;H[s]=!0;const v=s.endsWith(".css"),h=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${h}`))return;const u=document.createElement("link");if(u.rel=v?"stylesheet":et,v||(u.as="script"),u.crossOrigin="",u.href=s,g&&u.setAttribute("nonce",g),document.head.appendChild(u),v)return new Promise((w,S)=>{u.addEventListener("load",w),u.addEventListener("error",()=>S(new Error(`Unable to preload CSS for ${s}`)))})}))}function n(c){const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=c,window.dispatchEvent(r),!r.defaultPrevented)throw c}return a.then(c=>{for(const r of c||[])r.status==="rejected"&&n(r.reason);return d().catch(n)})},ot={class:"hero"},nt={class:"hero__background"},st={class:"container hero__container"},at={class:"hero__content"},it={class:"badge-text"},lt=["innerHTML"],rt={class:"stat"},ct={class:"stat-label"},dt={class:"stat"},ut={class:"stat-label"},vt={class:"stat"},ht={class:"stat-label"},pt={class:"visual-container"},mt={class:"floating-elements"},gt={class:"central-hub"},ft={class:"scroll-text"},yt=A({__name:"HeroSection",setup(m){const d=M(null),i=M(!1);let t=null,a,n;const c=()=>{console.log("Playing demo...")},r=()=>{if(!d.value)return;a=d.value,n=a.getContext("2d");const g=()=>{a.width=a.offsetWidth,a.height=a.offsetHeight};g(),window.addEventListener("resize",g);const s=[],v=()=>({x:-20,y:Math.random()*a.height,vx:1+Math.random()*2,vy:(Math.random()-.5)*.5,opacity:.3+Math.random()*.4});for(let u=0;u<5;u++)s.push(v());const h=()=>{n.clearRect(0,0,a.width,a.height),s.forEach((u,w)=>{if(u.x+=u.vx,u.y+=u.vy,u.x>a.width+20){s[w]=v();return}n.save(),n.globalAlpha=u.opacity,n.fillStyle="#00FFFF",n.shadowBlur=10,n.shadowColor="#00FFFF",n.beginPath(),n.arc(u.x,u.y,2,0,Math.PI*2),n.fill(),n.strokeStyle="#00FFFF",n.lineWidth=1,n.beginPath(),n.moveTo(u.x-20,u.y),n.lineTo(u.x,u.y),n.stroke(),n.restore()}),t=requestAnimationFrame(h)};h()};return R(()=>{setTimeout(()=>{i.value=!0},100),setTimeout(()=>{r()},500)}),O(()=>{t&&cancelAnimationFrame(t)}),(g,s)=>(b(),y("section",ot,[e("div",nt,[e("canvas",{ref_key:"heroCanvasRef",ref:d,class:"hero__canvas"},null,512)]),e("div",st,[e("div",at,[e("div",{class:f(["hero__badge",{"animate-fade-in":i.value}])},[e("span",it,o(g.$t("hero.badge")),1)],2),e("h1",{class:f(["hero__title",{"animate-slide-in-up delay-200":i.value}])},[e("span",{innerHTML:g.$t("hero.title",{highlight:`<span class='title-highlight'>${g.$t("hero.titleHighlight")}</span>`})},null,8,lt)],2),e("p",{class:f(["hero__subtitle",{"animate-slide-in-up delay-300":i.value}])},o(g.$t("hero.subtitle")),3),e("div",{class:f(["hero__actions",{"animate-slide-in-up delay-500":i.value}])},[p(_(k),{to:"/contact",class:"btn btn-primary hero__cta"},{default:$(()=>[e("span",null,o(g.$t("hero.exploreSolutions")),1),s[0]||(s[0]=e("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1))]),_:1,__:[0]}),e("button",{class:"btn btn-ghost hero__demo",onClick:c},[s[1]||(s[1]=e("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[e("polygon",{points:"5,3 19,12 5,21",fill:"currentColor"})],-1)),e("span",null,o(g.$t("hero.watchDemo")),1)])],2),e("div",{class:f(["hero__stats",{"animate-fade-in delay-700":i.value}])},[e("div",rt,[s[2]||(s[2]=e("div",{class:"stat-number"},"500+",-1)),e("div",ct,o(g.$t("hero.stats.projects")),1)]),e("div",dt,[s[3]||(s[3]=e("div",{class:"stat-number"},"50M+",-1)),e("div",ut,o(g.$t("hero.stats.devices")),1)]),e("div",vt,[s[4]||(s[4]=e("div",{class:"stat-number"},"99.9%",-1)),e("div",ht,o(g.$t("hero.stats.uptime")),1)])],2)]),e("div",{class:f(["hero__visual",{"animate-scale-in delay-400":i.value}])},[e("div",pt,[e("div",mt,[e("div",{class:f(["element element--1",{"animate-float":i.value}])},null,2),e("div",{class:f(["element element--2",{"animate-float delay-100":i.value}])},null,2),e("div",{class:f(["element element--3",{"animate-float delay-200":i.value}])},null,2)]),e("div",gt,[e("div",{class:f(["hub-core",{"animate-pulse-neon":i.value}])},null,2),s[5]||(s[5]=e("div",{class:"hub-rings"},[e("div",{class:"ring ring--1"}),e("div",{class:"ring ring--2"}),e("div",{class:"ring ring--3"})],-1))])])],2)]),e("div",{class:f(["hero__scroll-indicator",{"animate-fade-in delay-1000":i.value}])},[e("div",ft,o(g.$t("hero.scrollToExplore")),1),s[6]||(s[6]=e("div",{class:"scroll-arrow"},null,-1))],2)]))}}),_t=I(yt,[["__scopeId","data-v-c3de6142"]]),bt={class:"features section"},kt={class:"container"},$t={class:"features__header"},zt={class:"features__title"},wt={class:"features__subtitle"},Ct={class:"features__grid"},Tt={class:"feature-card__icon"},At={class:"icon-placeholder"},It={class:"feature-card__title"},St={class:"feature-card__description"},Pt={class:"feature-card__stats"},Mt={class:"stat"},jt={class:"stat-value"},Lt={class:"stat-label"},Et=A({__name:"FeaturesSection",setup(m){const d=M(!1),i=[{id:1,titleKey:"features.smartConnectivity.title",descriptionKey:"features.smartConnectivity.description",iconText:"⚡",stat:{value:"<1ms",labelKey:"stats.latency"}},{id:2,titleKey:"features.realTimeAnalytics.title",descriptionKey:"features.realTimeAnalytics.description",iconText:"🧠",stat:{value:"95%",labelKey:"stats.accuracy"}},{id:3,titleKey:"features.secureInfrastructure.title",descriptionKey:"features.secureInfrastructure.description",iconText:"🛡️",stat:{value:"256-bit",labelKey:"stats.encryption"}},{id:4,titleKey:"features.scalablePlatform.title",descriptionKey:"features.scalablePlatform.description",iconText:"📈",stat:{value:"10M+",labelKey:"stats.devices"}}];return R(()=>{const t=new IntersectionObserver(n=>{n.forEach(c=>{c.isIntersecting&&(d.value=!0)})},{threshold:.1}),a=document.querySelector(".features");a&&t.observe(a)}),(t,a)=>(b(),y("section",bt,[e("div",kt,[e("div",$t,[e("h2",zt,o(t.$t("features.title")),1),e("p",wt,o(t.$t("features.subtitle")),1)]),e("div",Ct,[(b(),y(D,null,V(i,(n,c)=>e("div",{key:n.id,class:f(["feature-card",{"animate-scale-in":d.value}]),style:Z({animationDelay:`${c*.1}s`})},[e("div",Tt,[e("div",At,o(n.iconText),1)]),e("h3",It,o(t.$t(n.titleKey)),1),e("p",St,o(t.$t(n.descriptionKey)),1),e("div",Pt,[e("div",Mt,[e("span",jt,o(n.stat.value),1),e("span",Lt,o(t.$t(n.stat.labelKey)),1)])])],6)),64))])])]))}}),Rt=I(Et,[["__scopeId","data-v-ad621711"]]),Ft={class:"technologies-preview section"},Dt={class:"container"},Vt={class:"technologies__header"},Ot={class:"technologies__title"},xt={class:"technologies__subtitle"},Kt={class:"technologies__content"},Nt={class:"tech-showcase"},Ht={class:"tech-visual"},Bt={class:"tech-nodes"},Zt=["onClick"],Wt={class:"node-icon"},Ut={class:"node-label"},qt={class:"tech-details"},Gt={key:0,class:"tech-card"},Jt={class:"tech-card__title"},Yt={class:"tech-card__description"},Qt={class:"tech-specs"},Xt={class:"spec-label"},eo={class:"spec-value"},to={class:"tech-features"},oo={class:"features-title"},no={class:"features-list"},so={class:"tech-stats"},ao={class:"stat-card"},io={class:"stat-label"},lo={class:"stat-card"},ro={class:"stat-label"},co={class:"stat-card"},uo={class:"stat-label"},vo=A({__name:"TechnologiesPreview",setup(m){const d=M(null),i=M(0),t=[{id:1,x:20,y:30,icon:"🔗",label:"technologies.connectivity.name"},{id:2,x:50,y:20,icon:"🧠",label:"technologies.aiAnalytics.name"},{id:3,x:80,y:35,icon:"☁️",label:"technologies.cloud.title"},{id:4,x:30,y:70,icon:"🔒",label:"technologies.security.name"},{id:5,x:70,y:75,icon:"📊",label:"technologies.ai.title"}],a=[{titleKey:"technologies.iot.title",descriptionKey:"technologies.iot.description",specs:[{label:"Protocols",value:"15+"},{label:"Range",value:"10km+"},{label:"Latency",value:"<1ms"}],features:["technologies.iot.features.autoDiscovery","technologies.iot.features.meshNetworking","technologies.iot.features.adaptiveProtocol","technologies.iot.features.edgeToCloud"]},{titleKey:"technologies.ai.title",descriptionKey:"technologies.ai.description",specs:[{label:"Models",value:"50+"},{label:"Accuracy",value:"99.5%"},{label:"Processing",value:"Real-time"}],features:["technologies.ai.features.predictiveAnalytics","technologies.ai.features.anomalyDetection","technologies.ai.features.nlp","technologies.ai.features.computerVision"]},{titleKey:"technologies.cloud.title",descriptionKey:"technologies.cloud.description",specs:[{label:"Uptime",value:"99.99%"},{label:"Regions",value:"25+"},{label:"Scale",value:"Unlimited"}],features:["technologies.cloud.features.autoScaling","technologies.cloud.features.globalEdge","technologies.cloud.features.multiCloud","technologies.cloud.features.disasterRecovery"]},{titleKey:"technologies.security.title",descriptionKey:"technologies.security.description",specs:[{label:"Encryption",value:"AES-256"},{label:"Compliance",value:"SOC2, ISO27001"},{label:"Authentication",value:"Multi-factor"}],features:["technologies.security.features.zeroTrust","technologies.security.features.blockchain","technologies.security.features.threatIntelligence","technologies.security.features.automatedUpdates"]}],n=B(()=>a[i.value]),c=s=>{i.value=s};let r=null;const g=()=>{if(!d.value)return;const s=d.value,v=s.getContext("2d"),h=()=>{s.width=s.offsetWidth,s.height=s.offsetHeight};h(),window.addEventListener("resize",h);const u=()=>{v.clearRect(0,0,s.width,s.height),v.strokeStyle="rgba(0, 255, 255, 0.3)",v.lineWidth=1;for(let w=0;w<t.length;w++)for(let S=w+1;S<t.length;S++){const j=t[w],L=t[S],l=j.x/100*s.width,z=j.y/100*s.height,P=L.x/100*s.width,T=L.y/100*s.height;v.beginPath(),v.moveTo(l,z),v.lineTo(P,T),v.stroke()}r=requestAnimationFrame(u)};u()};return R(()=>{setTimeout(()=>{g()},500);const s=setInterval(()=>{i.value=(i.value+1)%a.length},5e3);O(()=>{clearInterval(s),r&&cancelAnimationFrame(r)})}),(s,v)=>(b(),y("section",Ft,[e("div",Dt,[e("div",Vt,[e("h2",Ot,o(s.$t("technologies.title")),1),e("p",xt,o(s.$t("technologies.subtitle")),1)]),e("div",Kt,[e("div",Nt,[e("div",Ht,[e("canvas",{ref_key:"techCanvasRef",ref:d,class:"tech-canvas"},null,512),e("div",Bt,[(b(),y(D,null,V(t,(h,u)=>e("div",{key:h.id,class:f(["tech-node",{active:i.value===u}]),style:Z({left:h.x+"%",top:h.y+"%",animationDelay:`${u*.2}s`}),onClick:w=>c(u)},[e("div",Wt,o(h.icon),1),e("div",Ut,o(s.$t(h.label)),1)],14,Zt)),64))])]),e("div",qt,[n.value?(b(),y("div",Gt,[e("h3",Jt,o(s.$t(n.value.titleKey)),1),e("p",Yt,o(s.$t(n.value.descriptionKey)),1),e("div",Qt,[(b(!0),y(D,null,V(n.value.specs,h=>(b(),y("div",{key:h.label,class:"spec-item"},[e("span",Xt,o(h.label),1),e("span",eo,o(h.value),1)]))),128))]),e("div",to,[e("h4",oo,o(s.$t("technologies.keyFeatures")),1),e("ul",no,[(b(!0),y(D,null,V(n.value.features,h=>(b(),y("li",{key:h},o(s.$t(h)),1))),128))])])])):G("",!0)])]),e("div",so,[e("div",ao,[v[0]||(v[0]=e("div",{class:"stat-number"},"15+",-1)),e("div",io,o(s.$t("technologies.stats.technologies")),1)]),e("div",lo,[v[1]||(v[1]=e("div",{class:"stat-number"},"99.9%",-1)),e("div",ro,o(s.$t("technologies.stats.reliability")),1)]),e("div",co,[v[2]||(v[2]=e("div",{class:"stat-number"},"24/7",-1)),e("div",uo,o(s.$t("technologies.stats.support")),1)])])])])]))}}),ho=I(vo,[["__scopeId","data-v-fdd2a602"]]),po={class:"cta-section section"},mo={class:"container"},go={class:"cta-content"},fo={class:"cta-title"},yo={class:"cta-subtitle"},_o={class:"cta-actions"},bo={class:"cta-features"},ko={class:"feature-item"},$o={class:"feature-item"},zo={class:"feature-item"},wo=A({__name:"CTASection",setup(m){return(d,i)=>(b(),y("section",po,[e("div",mo,[e("div",go,[e("h2",fo,o(d.$t("cta.title")),1),e("p",yo,o(d.$t("cta.subtitle")),1),e("div",_o,[p(_(k),{to:"/contact",class:"btn btn-primary cta-primary"},{default:$(()=>[e("span",null,o(d.$t("cta.button")),1),i[0]||(i[0]=e("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1))]),_:1,__:[0]}),p(_(k),{to:"/solutions",class:"btn btn-outline cta-secondary"},{default:$(()=>[e("span",null,o(d.$t("cta.contact")),1)]),_:1})]),e("div",bo,[e("div",ko,[i[1]||(i[1]=e("span",{class:"feature-icon"},"✓",-1)),e("span",null,o(d.$t("cta.features.consultation")),1)]),e("div",$o,[i[2]||(i[2]=e("span",{class:"feature-icon"},"✓",-1)),e("span",null,o(d.$t("cta.features.trial")),1)]),e("div",zo,[i[3]||(i[3]=e("span",{class:"feature-icon"},"✓",-1)),e("span",null,o(d.$t("cta.features.support")),1)])])])])]))}}),Co=I(wo,[["__scopeId","data-v-edf89e9d"]]),To={class:"home-view"},Ao=A({__name:"HomeView",setup(m){return(d,i)=>(b(),y("div",To,[p(_t),p(Rt),p(ho),p(Co)]))}}),Io=I(Ao,[["__scopeId","data-v-4b95c547"]]),So=J({history:Y("/"),routes:[{path:"/",name:"home",component:Io},{path:"/solutions",name:"solutions",component:()=>F(()=>import("./SolutionsView-BDE0avr4.js"),__vite__mapDeps([0,1,2,3]))},{path:"/technologies",name:"technologies",component:()=>F(()=>import("./TechnologiesView-DwX5tSlO.js"),__vite__mapDeps([4,1,2,5]))},{path:"/about",name:"about",component:()=>F(()=>import("./AboutView-C5_t3nTL.js"),__vite__mapDeps([6,1,2,7]))},{path:"/contact",name:"contact",component:()=>F(()=>import("./ContactView-XsnzHoPy.js"),__vite__mapDeps([8,1,2,9]))}],scrollBehavior(m,d,i){return i||{top:0}}}),Po={home:"Home",solutions:"Solutions",technologies:"Technologies",about:"About",contact:"Contact",getStarted:"Get Started",switchLanguage:"Switch Language"},Mo={badge:"Next Generation IoT",title:"Welcome to the Future of Connected Intelligence",titleHighlight:"Future",subtitle:"Transform your world with cutting-edge IoT solutions that bridge the gap between imagination and reality. Where every device becomes intelligent, every connection meaningful, and every data point a step toward tomorrow.",exploreSolutions:"Explore Solutions",watchDemo:"Watch Demo",scrollToExplore:"Scroll to explore",stats:{projects:"Projects Delivered",devices:"Devices Connected",uptime:"Uptime Guaranteed"}},jo={title:"Revolutionary IoT Solutions",subtitle:"Discover how our advanced technology transforms industries and creates intelligent ecosystems",smartConnectivity:{title:"Smart Connectivity",description:"Seamless device integration with ultra-low latency and 99.9% reliability"},realTimeAnalytics:{title:"Real-time Analytics",description:"Advanced AI-powered insights that turn data into actionable intelligence"},secureInfrastructure:{title:"Secure Infrastructure",description:"Enterprise-grade security with end-to-end encryption and threat protection"},scalablePlatform:{title:"Scalable Platform",description:"Cloud-native architecture that grows with your business needs"}},Lo={title:"Cutting-Edge Technologies",subtitle:"Powered by the latest innovations in IoT, AI, and cloud computing",benefits:"Benefits",keyFeatures:"Key Features",technicalSpecs:"Technical Specifications",connectivity:{name:"Smart Connectivity",description:"Advanced connectivity solutions for seamless device integration",benefits:{multiProtocol:"Multi-protocol support",extendedRange:"Extended range connectivity",lowLatency:"Ultra-low latency",failover:"Automatic failover"},stats:{protocols:"Protocols",range:"Range",latency:"Latency"},features:{meshNetworking:{name:"Mesh Networking",description:"Self-healing network topology that automatically routes around failures",specs:{0:"Auto-discovery",1:"Load balancing",2:"Redundancy"}},edgeComputing:{name:"Edge Computing",description:"Process data locally to reduce latency and bandwidth usage",specs:{0:"Real-time processing",1:"Local storage",2:"Offline capability"}}}},aiAnalytics:{name:"AI Analytics",description:"Machine learning algorithms for predictive analytics and insights",benefits:{anomalyDetection:"Anomaly detection",predictiveMaintenance:"Predictive maintenance",automatedDecision:"Automated decision making",patternRecognition:"Pattern recognition"},stats:{models:"AI Models",accuracy:"Accuracy",processing:"Processing"},features:{machineLearning:{name:"Machine Learning",description:"Advanced ML models for pattern recognition and prediction",specs:{0:"Deep learning",1:"Neural networks",2:"Auto-training"}},dataAnalytics:{name:"Data Analytics",description:"Comprehensive analytics platform for business intelligence",specs:{0:"Real-time dashboards",1:"Custom reports",2:"API access"}}}},security:{name:"Security",description:"Enterprise-grade security with end-to-end encryption",benefits:{encryption:"End-to-end encryption",zeroTrust:"Zero-trust architecture",compliance:"Compliance ready",mfa:"Multi-factor authentication"},stats:{encryption:"Encryption",compliance:"Compliance",uptime:"Uptime"},features:{encryption:{name:"Encryption",description:"Military-grade encryption for all data transmission",specs:{0:"AES-256",1:"TLS 1.3",2:"Key rotation"}},accessControl:{name:"Access Control",description:"Granular permissions and role-based access control",specs:{0:"RBAC",1:"SSO",2:"MFA"}},zeroTrust:"Zero-trust architecture",blockchain:"Blockchain verification",threatIntelligence:"Threat intelligence",automatedUpdates:"Automated security updates"},title:"Cybersecurity"},iot:{title:"IoT Ecosystem",description:"Complete device management and orchestration platform",features:{autoDiscovery:"Auto-discovery and pairing",meshNetworking:"Mesh networking capabilities",adaptiveProtocol:"Adaptive protocol switching",edgeToCloud:"Edge-to-cloud connectivity"}},ai:{title:"Artificial Intelligence",description:"Machine learning algorithms for predictive analytics",features:{predictiveAnalytics:"Predictive analytics",anomalyDetection:"Anomaly detection",nlp:"Natural language processing",computerVision:"Computer vision integration"}},cloud:{title:"Cloud Infrastructure",description:"Scalable and reliable cloud-native solutions",features:{autoScaling:"Auto-scaling infrastructure",globalEdge:"Global edge deployment",multiCloud:"Multi-cloud support",disasterRecovery:"Disaster recovery"}},stats:{technologies:"Technologies",reliability:"Reliability",support:"Support"}},Eo={title:"Industry Solutions",subtitle:"Tailored IoT solutions for every industry vertical",keyFeatures:"Key Features",smartCities:{title:"Smart Cities",description:"Transform urban infrastructure with intelligent monitoring, traffic optimization, and energy management systems.",features:["Traffic Flow Optimization","Smart Lighting Systems","Waste Management","Environmental Monitoring"],statLabel:"Energy Savings"},manufacturing:{title:"Industrial Manufacturing",description:"Revolutionize production with predictive maintenance, quality control, and supply chain optimization.",features:["Predictive Maintenance","Quality Control Automation","Energy Optimization","Supply Chain Visibility"],statLabel:"Efficiency Increase"},healthcare:{title:"Healthcare & Medical",description:"Enhance patient care with remote monitoring, asset tracking, and intelligent medical devices.",features:["Patient Monitoring","Asset Tracking","Environmental Control","Emergency Response"],statLabel:"Response Time Improvement"},agriculture:{title:"Smart Agriculture",description:"Optimize crop yields with precision farming, soil monitoring, and automated irrigation systems.",features:["Soil Monitoring","Automated Irrigation","Crop Health Tracking","Weather Integration"],statLabel:"Yield Increase"}},Ro={title:"About IoTVision",subtitle:"Leading the future of connected intelligence",story:{title:"Our Story",paragraph1:"Founded in 2020 by a team of visionary engineers and data scientists, IoTVision emerged from a simple yet powerful belief: that the future belongs to those who can seamlessly connect the physical and digital worlds.",paragraph2:"Today, we're proud to be at the forefront of the IoT revolution, helping businesses across industries transform their operations through intelligent connectivity and data-driven insights."},stats:{projectsCompleted:"Projects Completed",devicesConnected:"Devices Connected",countriesServed:"Countries Served"},mission:{title:"Our Mission",description:"To democratize IoT technology and make intelligent connectivity accessible to businesses of all sizes, driving innovation and efficiency across industries."},vision:{title:"Our Vision",description:"A world where every device is intelligent, every connection is meaningful, and every data point contributes to a smarter, more sustainable future."},values:{title:"Our Values",description:"Innovation, reliability, and customer success drive everything we do. We believe in building lasting partnerships and delivering exceptional value."},team:{title:"Meet Our Team",members:{alexChen:{name:"Alex Chen",role:"CEO & Co-Founder",bio:"Visionary leader with 15+ years in IoT and enterprise technology."},sarahJohnson:{name:"Sarah Johnson",role:"CTO & Co-Founder",bio:"Technical architect specializing in scalable IoT infrastructure."},michaelRodriguez:{name:"Michael Rodriguez",role:"Head of AI/ML",bio:"AI researcher focused on intelligent edge computing solutions."},emilyZhang:{name:"Emily Zhang",role:"Head of Security",bio:"Cybersecurity expert ensuring enterprise-grade protection."}}}},Fo={title:"Get in Touch",subtitle:"Ready to transform your business with IoT? Let's discuss your project.",form:{name:"Full Name",email:"Email Address",company:"Company Name",phone:"Phone Number",subject:"Subject",message:"Message",send:"Send Message",sending:"Sending...",success:"Message sent successfully!",error:"Failed to send message. Please try again."},info:{address:"Prague, Czech Republic",email:"info[at]iotvision.com",phone:"+*********** 789",hours:"Mon-Fri: 9:00 - 18:00"},responseTime:{title:"Response Time",average:"Average Response",emergency:"Emergency Support"}},Do={title:"Ready to Start Your IoT Journey?",subtitle:"Join thousands of businesses already transforming with our IoT solutions",button:"Get Started Today",contact:"Contact Us",features:{consultation:"Free Consultation",trial:"30-Day Trial",support:"24/7 Support"}},Vo={learnMore:"Learn More"},Oo={latency:"Latency",accuracy:"Accuracy",encryption:"Encryption",devices:"Devices"},xo={description:"Leading provider of innovative IoT solutions for the connected world.",quickLinks:"Quick Links",solutions:"Solutions",services:{iotDevelopment:"IoT Development",smartHomeSolutions:"Smart Home Solutions",hardwareDesign:"Hardware Design",cloudIntegration:"Cloud Integration",consulting:"Consulting"},company:"Company",legal:"Legal",privacy:"Privacy Policy",terms:"Terms of Service",cookies:"Cookie Policy",copyright:"© 2024 IoTVision. All rights reserved.",madeWith:"Made with love using Vue.js 3 and modern web technologies."},Ko={nav:Po,hero:Mo,features:jo,technologies:Lo,solutions:Eo,about:Ro,contact:Fo,cta:Do,common:Vo,stats:Oo,footer:xo},No={home:"Domů",solutions:"Řešení",technologies:"Technologie",about:"O nás",contact:"Kontakt",getStarted:"Začít",switchLanguage:"Přepnout jazyk"},Ho={badge:"IoT Nové Generace",title:"Vítejte v budoucnosti propojené inteligence",titleHighlight:"budoucnosti",subtitle:"Transformujte svět pomocí špičkových IoT řešení, která překlenují propast mezi představivostí a realitou. Kde se každé zařízení stává inteligentním, každé spojení smysluplným a každý datový bod krokem k zítřku.",exploreSolutions:"Prozkoumat řešení",watchDemo:"Sledovat demo",scrollToExplore:"Rolujte pro prozkoumání",stats:{projects:"Dokončených projektů",devices:"Připojených zařízení",uptime:"Garantovaná dostupnost"}},Bo={title:"Revoluční IoT řešení",subtitle:"Objevte, jak naše pokročilá technologie transformuje odvětví a vytváří inteligentní ekosystémy",smartConnectivity:{title:"Chytré propojení",description:"Bezproblémová integrace zařízení s ultra-nízkou latencí a 99,9% spolehlivostí"},realTimeAnalytics:{title:"Analýzy v reálném čase",description:"Pokročilé poznatky poháněné AI, které mění data na užitečnou inteligenci"},secureInfrastructure:{title:"Bezpečná infrastruktura",description:"Podniková bezpečnost s end-to-end šifrováním a ochranou před hrozbami"},scalablePlatform:{title:"Škálovatelná platforma",description:"Cloud-native architektura, která roste s potřebami vašeho podnikání"}},Zo={title:"Špičkové technologie",subtitle:"Poháněno nejnovějšími inovacemi v IoT, AI a cloud computingu",benefits:"Výhody",keyFeatures:"Klíčové funkce",technicalSpecs:"Technické specifikace",connectivity:{name:"Chytrá konektivita",description:"Pokročilá řešení konektivity pro bezproblémovou integraci zařízení",benefits:{multiProtocol:"Podpora více protokolů",extendedRange:"Rozšířený dosah konektivity",lowLatency:"Ultra nízká latence",failover:"Automatické převzetí"},stats:{protocols:"Protokoly",range:"Dosah",latency:"Latence"},features:{meshNetworking:{name:"Mesh síťování",description:"Samoopravná síťová topologie, která automaticky směruje kolem selhání",specs:{0:"Automatické objevování",1:"Vyvažování zátěže",2:"Redundance"}},edgeComputing:{name:"Edge Computing",description:"Zpracování dat lokálně pro snížení latence a využití šířky pásma",specs:{0:"Zpracování v reálném čase",1:"Lokální úložiště",2:"Offline schopnost"}}}},aiAnalytics:{name:"AI Analytika",description:"Algoritmy strojového učení pro prediktivní analytiku a poznatky",benefits:{anomalyDetection:"Detekce anomálií",predictiveMaintenance:"Prediktivní údržba",automatedDecision:"Automatizované rozhodování",patternRecognition:"Rozpoznávání vzorů"},stats:{models:"AI Modely",accuracy:"Přesnost",processing:"Zpracování"},features:{machineLearning:{name:"Strojové učení",description:"Pokročilé ML modely pro rozpoznávání vzorů a predikci",specs:{0:"Hluboké učení",1:"Neuronové sítě",2:"Automatické trénování"}},dataAnalytics:{name:"Analýza dat",description:"Komplexní analytická platforma pro business intelligence",specs:{0:"Dashboardy v reálném čase",1:"Vlastní reporty",2:"API přístup"}}}},security:{name:"Bezpečnost",description:"Podniková bezpečnost s end-to-end šifrováním",benefits:{encryption:"End-to-end šifrování",zeroTrust:"Zero-trust architektura",compliance:"Připravenost pro compliance",mfa:"Vícefaktorová autentizace"},stats:{encryption:"Šifrování",compliance:"Compliance",uptime:"Dostupnost"},features:{encryption:{name:"Šifrování",description:"Vojenské šifrování pro veškerou datovou transmisi",specs:{0:"AES-256",1:"TLS 1.3",2:"Rotace klíčů"}},accessControl:{name:"Řízení přístupu",description:"Granulární oprávnění a řízení přístupu založené na rolích",specs:{0:"RBAC",1:"SSO",2:"MFA"}},zeroTrust:"Zero-trust architektura",blockchain:"Blockchain ověření",threatIntelligence:"Threat intelligence",automatedUpdates:"Automatické bezpečnostní aktualizace"},title:"Kybernetická bezpečnost"},iot:{title:"IoT ekosystém",description:"Kompletní platforma pro správu a orchestraci zařízení",features:{autoDiscovery:"Automatické objevování a párování",meshNetworking:"Možnosti mesh sítí",adaptiveProtocol:"Adaptivní přepínání protokolů",edgeToCloud:"Konektivita edge-to-cloud"}},ai:{title:"Umělá inteligence",description:"Algoritmy strojového učení pro prediktivní analýzy",features:{predictiveAnalytics:"Prediktivní analytika",anomalyDetection:"Detekce anomálií",nlp:"Zpracování přirozeného jazyka",computerVision:"Integrace počítačového vidění"}},cloud:{title:"Cloudová infrastruktura",description:"Škálovatelná a spolehlivá cloud-native řešení",features:{autoScaling:"Automaticky škálující infrastruktura",globalEdge:"Globální edge nasazení",multiCloud:"Podpora více cloudů",disasterRecovery:"Obnova po havárii"}},stats:{technologies:"Technologie",reliability:"Spolehlivost",support:"Podpora"}},Wo={title:"Odvětvová řešení",subtitle:"Přizpůsobená IoT řešení pro každé odvětví",keyFeatures:"Klíčové funkce",smartCities:{title:"Chytrá města",description:"Transformujte městskou infrastrukturu pomocí inteligentního monitoringu, optimalizace dopravy a systémů řízení energie.",features:["Optimalizace dopravního toku","Chytré osvětlovací systémy","Správa odpadu","Monitorování životního prostředí"],statLabel:"Úspora energie"},manufacturing:{title:"Průmyslová výroba",description:"Revolucionalizujte výrobu pomocí prediktivní údržby, kontroly kvality a optimalizace dodavatelského řetězce.",features:["Prediktivní údržba","Automatizace kontroly kvality","Optimalizace energie","Viditelnost dodavatelského řetězce"],statLabel:"Zvýšení efektivity"},healthcare:{title:"Zdravotnictví a medicína",description:"Vylepšete péči o pacienty pomocí vzdáleného monitoringu, sledování majetku a inteligentních zdravotnických zařízení.",features:["Monitorování pacientů","Sledování majetku","Kontrola prostředí","Nouzová reakce"],statLabel:"Zlepšení reakčního času"},agriculture:{title:"Chytré zemědělství",description:"Optimalizujte výnosy plodin pomocí precizního zemědělství, monitoringu půdy a automatizovaných zavlažovacích systémů.",features:["Monitorování půdy","Automatizované zavlažování","Sledování zdraví plodin","Integrace počasí"],statLabel:"Zvýšení výnosu"}},Uo={title:"O společnosti IoTVision",subtitle:"Vedeme budoucnost propojené inteligence",story:{title:"Náš příběh",paragraph1:"Založena v roce 2020 týmem vizionářských inženýrů a datových vědců, IoTVision vznikla z jednoduchého, ale mocného přesvědčení: že budoucnost patří těm, kteří dokážou bezproblémově propojit fyzický a digitální svět.",paragraph2:"Dnes jsme hrdí na to, že jsme v čele IoT revoluce a pomáháme podnikům napříč odvětvími transformovat své operace prostřednictvím inteligentní konektivity a poznatků založených na datech."},stats:{projectsCompleted:"Dokončených projektů",devicesConnected:"Připojených zařízení",countriesServed:"Obsluhovaných zemí"},mission:{title:"Naše mise",description:"Demokratizovat IoT technologie a zpřístupnit inteligentní konektivitu podnikům všech velikostí, podporovat inovace a efektivitu napříč odvětvími."},vision:{title:"Naše vize",description:"Svět, kde je každé zařízení inteligentní, každé spojení smysluplné a každý datový bod přispívá k chytřejší, udržitelnější budoucnosti."},values:{title:"Naše hodnoty",description:"Inovace, spolehlivost a úspěch zákazníků řídí vše, co děláme. Věříme v budování trvalých partnerství a poskytování výjimečné hodnoty."},team:{title:"Poznejte náš tým",members:{alexChen:{name:"Alex Chen",role:"CEO a spoluzakladatel",bio:"Vizionářský lídr s více než 15 lety zkušeností v IoT a podnikových technologiích."},sarahJohnson:{name:"Sarah Johnson",role:"CTO a spoluzakladatelka",bio:"Technický architekt specializující se na škálovatelnou IoT infrastrukturu."},michaelRodriguez:{name:"Michael Rodriguez",role:"Vedoucí AI/ML",bio:"AI výzkumník zaměřený na inteligentní edge computing řešení."},emilyZhang:{name:"Emily Zhang",role:"Vedoucí bezpečnosti",bio:"Kybernetická bezpečnostní expertka zajišťující podnikovou ochranu."}}}},qo={title:"Kontaktujte nás",subtitle:"Připraveni transformovat své podnikání pomocí IoT? Pojďme diskutovat o vašem projektu.",form:{name:"Celé jméno",email:"E-mailová adresa",company:"Název společnosti",phone:"Telefonní číslo",subject:"Předmět",message:"Zpráva",send:"Odeslat zprávu",sending:"Odesílání...",success:"Zpráva byla úspěšně odeslána!",error:"Nepodařilo se odeslat zprávu. Zkuste to prosím znovu."},info:{address:"Praha, Česká republika",email:"info[at]iotvision.com",phone:"+*********** 789",hours:"Po-Pá: 9:00 - 18:00"},responseTime:{title:"Doba odezvy",average:"Průměrná odezva",emergency:"Nouzová podpora"}},Go={title:"Připraveni začít svou IoT cestu?",subtitle:"Připojte se k tisícům podniků, které se již transformují pomocí našich IoT řešení",button:"Začít ještě dnes",contact:"Kontaktujte nás",features:{consultation:"Bezplatná konzultace",trial:"30denní zkušební verze",support:"Podpora 24/7"}},Jo={learnMore:"Zjistit více"},Yo={latency:"Latence",accuracy:"Přesnost",encryption:"Šifrování",devices:"Zařízení"},Qo={description:"Přední poskytovatel inovativních IoT řešení pro propojený svět.",quickLinks:"Rychlé odkazy",solutions:"Řešení",services:{iotDevelopment:"Vývoj IoT",smartHomeSolutions:"Řešení chytré domácnosti",hardwareDesign:"Návrh hardwaru",cloudIntegration:"Cloudová integrace",consulting:"Poradenství"},company:"Společnost",legal:"Právní",privacy:"Zásady ochrany osobních údajů",terms:"Podmínky služby",cookies:"Zásady cookies",copyright:"© 2024 IoTVision. Všechna práva vyhrazena.",madeWith:"Vytvořeno s láskou pomocí Vue.js 3 a moderních webových technologií."},Xo={nav:No,hero:Ho,features:Bo,technologies:Zo,solutions:Wo,about:Uo,contact:qo,cta:Go,common:Jo,stats:Yo,footer:Qo},en=()=>{const m=localStorage.getItem("preferred-language");return m&&["en","cs"].includes(m)?m:(navigator.language||navigator.userLanguage).toLowerCase().startsWith("en")?"en":"cs"},tn=te({legacy:!1,locale:en(),fallbackLocale:"cs",globalInjection:!0,messages:{en:Ko,cs:Xo}}),x=Q(Xe);x.use(X());x.use(So);x.use(tn);x.mount("#app");export{I as _};
